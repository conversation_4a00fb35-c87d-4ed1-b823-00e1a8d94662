{"numTotalTestSuites": 26, "numPassedTestSuites": 26, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 77, "numPassedTests": 76, "numFailedTests": 1, "numPendingTests": 0, "numTodoTests": 0, "startTime": 1756370335909, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["", "Component Tests"], "fullName": " Component Tests button component works", "status": "passed", "title": "button component works", "duration": 713, "failureMessages": []}, {"ancestorTitles": ["", "Component Tests"], "fullName": " Component Tests input component works", "status": "passed", "title": "input component works", "duration": 32, "failureMessages": []}, {"ancestorTitles": ["", "Component Tests"], "fullName": " Component Tests modal component works", "status": "passed", "title": "modal component works", "duration": 70, "failureMessages": []}, {"ancestorTitles": ["", "Component Tests"], "fullName": " Component Tests progress bar component works", "status": "passed", "title": "progress bar component works", "duration": 39, "failureMessages": []}, {"ancestorTitles": ["", "Component Tests"], "fullName": " Component Tests tooltip component works", "status": "passed", "title": "tooltip component works", "duration": 47, "failureMessages": []}, {"ancestorTitles": ["", "Component Tests"], "fullName": " Component Tests accordion component works", "status": "passed", "title": "accordion component works", "duration": 31, "failureMessages": []}, {"ancestorTitles": ["", "Component Tests"], "fullName": " Component Tests table sorting works", "status": "passed", "title": "table sorting works", "duration": 121, "failureMessages": []}, {"ancestorTitles": ["", "Component Tests"], "fullName": " Component Tests pagination works", "status": "passed", "title": "pagination works", "duration": 9, "failureMessages": []}, {"ancestorTitles": ["", "Component Tests"], "fullName": " Component Tests form validation works", "status": "passed", "title": "form validation works", "duration": 12, "failureMessages": []}, {"ancestorTitles": ["", "Component Tests"], "fullName": " Component Tests search filtering works", "status": "passed", "title": "search filtering works", "duration": 9, "failureMessages": []}], "startTime": 1756370339818, "endTime": 1756370340903, "status": "passed", "message": "", "name": "E:/archon-turbo/archon-ui-main/test/components.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["", "Page Load Tests"], "fullName": " Page Load Tests simple page component renders", "status": "passed", "title": "simple page component renders", "duration": 50, "failureMessages": []}, {"ancestorTitles": ["", "Page Load Tests"], "fullName": " Page Load Tests knowledge base mock renders", "status": "passed", "title": "knowledge base mock renders", "duration": 3, "failureMessages": []}, {"ancestorTitles": ["", "Page Load Tests"], "fullName": " Page Load Tests settings mock renders", "status": "passed", "title": "settings mock renders", "duration": 3, "failureMessages": []}, {"ancestorTitles": ["", "Page Load Tests"], "fullName": " Page Load Tests mcp mock renders", "status": "passed", "title": "mcp mock renders", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "Page Load Tests"], "fullName": " Page Load Tests tasks mock renders", "status": "passed", "title": "tasks mock renders", "duration": 6, "failureMessages": []}, {"ancestorTitles": ["", "Page Load Tests"], "fullName": " Page Load Tests onboarding page renders", "status": "passed", "title": "onboarding page renders", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "Onboarding Detection Tests"], "fullName": " Onboarding Detection Tests isLmConfigured returns true when provider is openai and OPENAI_API_KEY exists", "status": "passed", "title": "isLmConfigured returns true when provider is openai and OPENAI_API_KEY exists", "duration": 4, "failureMessages": []}, {"ancestorTitles": ["", "Onboarding Detection Tests"], "fullName": " Onboarding Detection Tests isLmConfigured returns true when provider is openai and OPENAI_API_KEY is encrypted", "status": "passed", "title": "isLmConfigured returns true when provider is openai and OPENAI_API_KEY is encrypted", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Onboarding Detection Tests"], "fullName": " Onboarding Detection Tests isLmConfigured returns false when provider is openai and no OPENAI_API_KEY", "status": "passed", "title": "isLmConfigured returns false when provider is openai and no OPENAI_API_KEY", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Onboarding Detection Tests"], "fullName": " Onboarding Detection Tests isLmConfigured returns true when provider is ollama regardless of API keys", "status": "passed", "title": "isLmConfigured returns true when provider is ollama regardless of API keys", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Onboarding Detection Tests"], "fullName": " Onboarding Detection Tests isLmConfigured returns true when no provider but OPENAI_API_KEY exists", "status": "failed", "title": "isLmConfigured returns true when no provider but OPENAI_API_KEY exists", "duration": 14, "failureMessages": ["expected false to be true // Object.is equality"], "location": {"line": 107, "column": 51}}, {"ancestorTitles": ["", "Onboarding Detection Tests"], "fullName": " Onboarding Detection Tests isLmConfigured returns false when no provider and no OPENAI_API_KEY", "status": "passed", "title": "isLmConfigured returns false when no provider and no OPENAI_API_KEY", "duration": 0, "failureMessages": []}], "startTime": 1756370338776, "endTime": 1756370338867, "status": "failed", "message": "", "name": "E:/archon-turbo/archon-ui-main/test/pages.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["", "User Flow Tests"], "fullName": " User Flow Tests create project flow mock", "status": "passed", "title": "create project flow mock", "duration": 179, "failureMessages": []}, {"ancestorTitles": ["", "User Flow Tests"], "fullName": " User Flow Tests search functionality mock", "status": "passed", "title": "search functionality mock", "duration": 17, "failureMessages": []}, {"ancestorTitles": ["", "User Flow Tests"], "fullName": " User Flow Tests settings toggle mock", "status": "passed", "title": "settings toggle mock", "duration": 10, "failureMessages": []}, {"ancestorTitles": ["", "User Flow Tests"], "fullName": " User Flow Tests file upload mock", "status": "passed", "title": "file upload mock", "duration": 7, "failureMessages": []}, {"ancestorTitles": ["", "User Flow Tests"], "fullName": " User Flow Tests connection status mock", "status": "passed", "title": "connection status mock", "duration": 7, "failureMessages": []}, {"ancestorTitles": ["", "User Flow Tests"], "fullName": " User Flow Tests task management mock", "status": "passed", "title": "task management mock", "duration": 9, "failureMessages": []}, {"ancestorTitles": ["", "User Flow Tests"], "fullName": " User Flow Tests navigation mock", "status": "passed", "title": "navigation mock", "duration": 7, "failureMessages": []}, {"ancestorTitles": ["", "User Flow Tests"], "fullName": " User Flow Tests form validation mock", "status": "passed", "title": "form validation mock", "duration": 17, "failureMessages": []}, {"ancestorTitles": ["", "User Flow Tests"], "fullName": " User Flow Tests theme switching mock", "status": "passed", "title": "theme switching mock", "duration": 6, "failureMessages": []}, {"ancestorTitles": ["", "User Flow Tests"], "fullName": " User Flow Tests data filtering mock", "status": "passed", "title": "data filtering mock", "duration": 9, "failureMessages": []}], "startTime": 1756370338762, "endTime": 1756370339030, "status": "passed", "message": "", "name": "E:/archon-turbo/archon-ui-main/test/user_flows.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["", "Error <PERSON>"], "fullName": " Error Handling Tests api error simulation", "status": "passed", "title": "api error simulation", "duration": 141, "failureMessages": []}, {"ancestorTitles": ["", "Error <PERSON>"], "fullName": " Error Handling Tests timeout error simulation", "status": "passed", "title": "timeout error simulation", "duration": 6, "failureMessages": []}, {"ancestorTitles": ["", "Error <PERSON>"], "fullName": " Error Handling Tests form validation errors", "status": "passed", "title": "form validation errors", "duration": 19, "failureMessages": []}, {"ancestorTitles": ["", "Error <PERSON>"], "fullName": " Error Handling Tests connection error recovery", "status": "passed", "title": "connection error recovery", "duration": 18, "failureMessages": []}, {"ancestorTitles": ["", "Error <PERSON>"], "fullName": " Error Handling Tests user friendly error messages", "status": "passed", "title": "user friendly error messages", "duration": 29, "failureMessages": []}, {"ancestorTitles": ["", "CredentialsService E<PERSON><PERSON>"], "fullName": " CredentialsService Error Handling should handle network errors with context", "status": "passed", "title": "should handle network errors with context", "duration": 5, "failureMessages": []}, {"ancestorTitles": ["", "CredentialsService E<PERSON><PERSON>"], "fullName": " CredentialsService Error Handling should preserve context in error messages", "status": "passed", "title": "should preserve context in error messages", "duration": 1, "failureMessages": []}], "startTime": 1756370338810, "endTime": 1756370339031, "status": "passed", "message": "", "name": "E:/archon-turbo/archon-ui-main/test/errors.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["", "projectService Document Operations", "getDocument"], "fullName": " projectService Document Operations getDocument should successfully fetch a document", "status": "passed", "title": "should successfully fetch a document", "duration": 108, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "getDocument"], "fullName": " projectService Document Operations getDocument should include projectId in error message when fetch fails", "status": "passed", "title": "should include projectId in error message when fetch fails", "duration": 10, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "getDocument"], "fullName": " projectService Document Operations getDocument should handle network errors", "status": "passed", "title": "should handle network errors", "duration": 7, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "updateDocument"], "fullName": " projectService Document Operations updateDocument should successfully update a document", "status": "passed", "title": "should successfully update a document", "duration": 13, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "updateDocument"], "fullName": " projectService Document Operations updateDocument should include projectId in error message when update fails", "status": "passed", "title": "should include projectId in error message when update fails", "duration": 7, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "updateDocument"], "fullName": " projectService Document Operations updateDocument should handle partial updates", "status": "passed", "title": "should handle partial updates", "duration": 7, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "deleteDocument"], "fullName": " projectService Document Operations deleteDocument should successfully delete a document", "status": "passed", "title": "should successfully delete a document", "duration": 8, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "deleteDocument"], "fullName": " projectService Document Operations deleteDocument should include projectId in error message when deletion fails", "status": "passed", "title": "should include projectId in error message when deletion fails", "duration": 6, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "deleteDocument"], "fullName": " projectService Document Operations deleteDocument should handle 404 errors appropriately", "status": "passed", "title": "should handle 404 errors appropriately", "duration": 7, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "deleteDocument"], "fullName": " projectService Document Operations deleteDocument should handle network timeouts", "status": "passed", "title": "should handle network timeouts", "duration": 8, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "listProjectDocuments"], "fullName": " projectService Document Operations listProjectDocuments should successfully list all project documents", "status": "passed", "title": "should successfully list all project documents", "duration": 7, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "listProjectDocuments"], "fullName": " projectService Document Operations listProjectDocuments should return empty array when no documents exist", "status": "passed", "title": "should return empty array when no documents exist", "duration": 6, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "listProjectDocuments"], "fullName": " projectService Document Operations listProjectDocuments should handle null documents field gracefully", "status": "passed", "title": "should handle null documents field gracefully", "duration": 6, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "createDocument"], "fullName": " projectService Document Operations createDocument should successfully create a new document", "status": "passed", "title": "should successfully create a new document", "duration": 8, "failureMessages": []}, {"ancestorTitles": ["", "projectService Document Operations", "createDocument"], "fullName": " projectService Document Operations createDocument should handle validation errors", "status": "passed", "title": "should handle validation errors", "duration": 6, "failureMessages": []}], "startTime": 1756370338751, "endTime": 1756370338967, "status": "passed", "message": "", "name": "E:/archon-turbo/archon-ui-main/test/services/projectService.test.ts"}, {"assertionResults": [{"ancestorTitles": ["", "DocsTab Document Cards Integration"], "fullName": " DocsTab Document Cards Integration renders all document cards", "status": "passed", "title": "renders all document cards", "duration": 58, "failureMessages": []}, {"ancestorTitles": ["", "DocsTab Document Cards Integration"], "fullName": " DocsTab Document Cards Integration shows active state on selected document", "status": "passed", "title": "shows active state on selected document", "duration": 8, "failureMessages": []}, {"ancestorTitles": ["", "DocsTab Document Cards Integration"], "fullName": " DocsTab Document Cards Integration switches between documents", "status": "passed", "title": "switches between documents", "duration": 24, "failureMessages": []}, {"ancestorTitles": ["", "DocsTab Document Cards Integration"], "fullName": " DocsTab Document Cards Integration deletes document with confirmation", "status": "passed", "title": "deletes document with confirmation", "duration": 13, "failureMessages": []}, {"ancestorTitles": ["", "DocsTab Document Cards Integration"], "fullName": " DocsTab Document Cards Integration cancels delete when user declines", "status": "passed", "title": "cancels delete when user declines", "duration": 8, "failureMessages": []}, {"ancestorTitles": ["", "DocsTab Document Cards Integration"], "fullName": " DocsTab Document Cards Integration selects next document when deleting active document", "status": "passed", "title": "selects next document when deleting active document", "duration": 15, "failureMessages": []}, {"ancestorTitles": ["", "DocsTab Document Cards Integration"], "fullName": " DocsTab Document Cards Integration does not show delete button on active card", "status": "passed", "title": "does not show delete button on active card", "duration": 7, "failureMessages": []}, {"ancestorTitles": ["", "DocsTab Document Cards Integration"], "fullName": " DocsTab Document Cards Integration horizontal scroll container has correct classes", "status": "passed", "title": "horizontal scroll container has correct classes", "duration": 15, "failureMessages": []}, {"ancestorTitles": ["", "DocsTab Document Cards Integration"], "fullName": " DocsTab Document Cards Integration document cards maintain fixed width", "status": "passed", "title": "document cards maintain fixed width", "duration": 7, "failureMessages": []}, {"ancestorTitles": ["", "DocsTab Document API Integration"], "fullName": " DocsTab Document API Integration calls deleteDocument API when deleting a document", "status": "passed", "title": "calls deleteDocument API when deleting a document", "duration": 15, "failureMessages": []}, {"ancestorTitles": ["", "DocsTab Document API Integration"], "fullName": " DocsTab Document API Integration handles deletion API errors gracefully", "status": "passed", "title": "handles deletion API errors gracefully", "duration": 8, "failureMessages": []}, {"ancestorTitles": ["", "DocsTab Document API Integration"], "fullName": " DocsTab Document API Integration deletion persists after page refresh", "status": "passed", "title": "deletion persists after page refresh", "duration": 6, "failureMessages": []}], "startTime": 1756370338769, "endTime": 1756370338953, "status": "passed", "message": "", "name": "E:/archon-turbo/archon-ui-main/test/components/project-tasks/DocsTab.integration.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["", "API Configuration", "getApiUrl"], "fullName": " API Configuration getApiUrl should use VITE_API_URL when provided", "status": "passed", "title": "should use VITE_API_URL when provided", "duration": 40, "failureMessages": []}, {"ancestorTitles": ["", "API Configuration", "getApiUrl"], "fullName": " API Configuration getApiUrl should return empty string in production mode", "status": "passed", "title": "should return empty string in production mode", "duration": 4, "failureMessages": []}, {"ancestorTitles": ["", "API Configuration", "getApiUrl"], "fullName": " API Configuration getApiUrl should use default port 8181 when no port environment variables are set in development", "status": "passed", "title": "should use default port 8181 when no port environment variables are set in development", "duration": 6, "failureMessages": []}, {"ancestorTitles": ["", "API Configuration", "getApiUrl"], "fullName": " API Configuration getApiUrl should use VITE_ARCHON_SERVER_PORT when set in development", "status": "passed", "title": "should use VITE_ARCHON_SERVER_PORT when set in development", "duration": 4, "failureMessages": []}, {"ancestorTitles": ["", "API Configuration", "getApiUrl"], "fullName": " API Configuration getApiUrl should use custom port with https protocol", "status": "passed", "title": "should use custom port with https protocol", "duration": 4, "failureMessages": []}, {"ancestorTitles": ["", "API Configuration", "getWebSocketUrl"], "fullName": " API Configuration getWebSocketUrl should convert http to ws", "status": "passed", "title": "should convert http to ws", "duration": 4, "failureMessages": []}, {"ancestorTitles": ["", "API Configuration", "getWebSocketUrl"], "fullName": " API Configuration getWebSocketUrl should convert https to wss", "status": "passed", "title": "should convert https to wss", "duration": 3, "failureMessages": []}, {"ancestorTitles": ["", "API Configuration", "getWebSocketUrl"], "fullName": " API Configuration getWebSocketUrl should handle production mode with https", "status": "passed", "title": "should handle production mode with https", "duration": 4, "failureMessages": []}, {"ancestorTitles": ["", "API Configuration", "Port validation"], "fullName": " API Configuration Port validation should handle various port formats", "status": "passed", "title": "should handle various port formats", "duration": 7, "failureMessages": []}, {"ancestorTitles": ["", "MCP Client Service Configuration"], "fullName": " MCP Client Service Configuration should throw error when ARCHON_MCP_PORT is not set", "status": "passed", "title": "should throw error when ARCHON_MCP_PORT is not set", "duration": 55, "failureMessages": []}, {"ancestorTitles": ["", "MCP Client Service Configuration"], "fullName": " MCP Client Service Configuration should use ARCHON_MCP_PORT when set", "status": "passed", "title": "should use ARCHON_MCP_PORT when set", "duration": 12, "failureMessages": []}], "startTime": 1756370338754, "endTime": 1756370338897, "status": "passed", "message": "", "name": "E:/archon-turbo/archon-ui-main/test/config/api.test.ts"}]}