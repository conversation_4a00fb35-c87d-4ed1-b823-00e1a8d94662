{"total": {"lines": {"total": 38818, "covered": 1206, "skipped": 0, "pct": 3.1}, "statements": {"total": 38818, "covered": 1206, "skipped": 0, "pct": 3.1}, "functions": {"total": 202, "covered": 18, "skipped": 0, "pct": 8.91}, "branches": {"total": 208, "covered": 67, "skipped": 0, "pct": 32.21}, "branchesTrue": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "E:\\archon-turbo\\archon-ui-main\\src\\App.tsx": {"lines": {"total": 102, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 102, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\index.tsx": {"lines": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\BackendStartupError.tsx": {"lines": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\DisconnectScreenOverlay.tsx": {"lines": {"total": 47, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 47, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ProjectCreationProgressCard.tsx": {"lines": {"total": 289, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 289, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\animations\\Animations.tsx": {"lines": {"total": 118, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 118, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\animations\\DisconnectScreenAnimations.tsx": {"lines": {"total": 175, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 175, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\bug-report\\BugReportButton.tsx": {"lines": {"total": 54, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 54, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\bug-report\\BugReportModal.tsx": {"lines": {"total": 417, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 417, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\bug-report\\ErrorBoundaryWithBugReport.tsx": {"lines": {"total": 178, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 178, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\code\\CodeViewerModal.tsx": {"lines": {"total": 442, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 442, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\knowledge-base\\CrawlingProgressCard.tsx": {"lines": {"total": 931, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 931, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\knowledge-base\\EditKnowledgeItemModal.tsx": {"lines": {"total": 277, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 277, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\knowledge-base\\GroupCreationModal.tsx": {"lines": {"total": 158, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 158, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\knowledge-base\\GroupedKnowledgeItemCard.tsx": {"lines": {"total": 665, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 665, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\knowledge-base\\KnowledgeItemCard.tsx": {"lines": {"total": 522, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 522, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\knowledge-base\\KnowledgeItemSkeleton.tsx": {"lines": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\knowledge-base\\KnowledgeTable.tsx": {"lines": {"total": 335, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 335, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\layouts\\ArchonChatPanel.tsx": {"lines": {"total": 479, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 479, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\layouts\\MainLayout.tsx": {"lines": {"total": 215, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 215, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\layouts\\SideNavigation.tsx": {"lines": {"total": 130, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 130, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\mcp\\ClientCard.tsx": {"lines": {"total": 508, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 508, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\mcp\\MCPClients.tsx": {"lines": {"total": 858, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 858, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\mcp\\ToolTestingPanel.tsx": {"lines": {"total": 568, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 568, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\onboarding\\ProviderStep.tsx": {"lines": {"total": 313, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 313, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\DataTab.tsx": {"lines": {"total": 958, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 958, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\DocsTab.tsx": {"lines": {"total": 1536, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 1536, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\DocumentCard.tsx": {"lines": {"total": 148, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 148, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\DraggableTaskCard.tsx": {"lines": {"total": 257, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 257, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\EditTaskModal.tsx": {"lines": {"total": 243, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 243, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\FeaturesTab.tsx": {"lines": {"total": 814, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 814, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\MilkdownEditor.tsx": {"lines": {"total": 555, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 555, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\Tabs.tsx": {"lines": {"total": 135, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 135, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\TaskBoardView.tsx": {"lines": {"total": 397, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 397, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\TaskInputComponents.tsx": {"lines": {"total": 172, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 172, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\TaskTableView.tsx": {"lines": {"total": 891, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 891, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\TasksTab.tsx": {"lines": {"total": 680, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 680, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\project-tasks\\VersionHistoryModal.tsx": {"lines": {"total": 661, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 661, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\PRPViewer.tsx": {"lines": {"total": 279, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 279, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\index.ts": {"lines": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\components\\CollapsibleSectionRenderer.tsx": {"lines": {"total": 411, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 411, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\components\\CollapsibleSectionWrapper.tsx": {"lines": {"total": 80, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 80, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\components\\MarkdownDocumentRenderer.tsx": {"lines": {"total": 323, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 323, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\components\\MarkdownSectionRenderer.tsx": {"lines": {"total": 71, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 71, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\components\\SimpleMarkdown.tsx": {"lines": {"total": 340, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 340, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\renderers\\SectionRenderer.tsx": {"lines": {"total": 141, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 141, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\ContextSection.tsx": {"lines": {"total": 82, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 82, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\FeatureSection.tsx": {"lines": {"total": 155, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 155, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\FlowSection.tsx": {"lines": {"total": 72, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 72, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\GenericSection.tsx": {"lines": {"total": 233, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 233, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\KeyValueSection.tsx": {"lines": {"total": 111, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 111, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\ListSection.tsx": {"lines": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\MetadataSection.tsx": {"lines": {"total": 85, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 85, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\MetricsSection.tsx": {"lines": {"total": 74, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 74, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\ObjectSection.tsx": {"lines": {"total": 193, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 193, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\PersonaSection.tsx": {"lines": {"total": 184, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 184, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\PlanSection.tsx": {"lines": {"total": 136, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 136, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\RolloutPlanSection.tsx": {"lines": {"total": 236, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 236, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\sections\\TokenSystemSection.tsx": {"lines": {"total": 235, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 235, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\types\\prp.types.ts": {"lines": {"total": 121, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 121, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\utils\\formatters.ts": {"lines": {"total": 53, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 53, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\utils\\markdownParser.ts": {"lines": {"total": 397, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 397, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\utils\\normalizer.ts": {"lines": {"total": 211, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 211, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\utils\\objectRenderer.tsx": {"lines": {"total": 107, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 107, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\prp\\utils\\sectionDetector.ts": {"lines": {"total": 204, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 204, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\settings\\ButtonPlayground.tsx": {"lines": {"total": 674, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 674, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\settings\\CodeExtractionSettings.tsx": {"lines": {"total": 311, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 311, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\settings\\FeaturesSection.tsx": {"lines": {"total": 319, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 319, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\settings\\IDEGlobalRules.tsx": {"lines": {"total": 567, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 567, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\settings\\RAGSettings.tsx": {"lines": {"total": 476, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 476, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\settings\\TestStatus.tsx": {"lines": {"total": 704, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 704, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\settings\\ModelConfiguration\\ChatModelSection.tsx": {"lines": {"total": 124, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 124, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\settings\\ModelConfiguration\\EmbeddingModelSection.tsx": {"lines": {"total": 124, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 124, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\settings\\ModelConfiguration\\ModelConfiguration.tsx": {"lines": {"total": 33, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 33, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\Badge.tsx": {"lines": {"total": 39, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 39, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\Button.tsx": {"lines": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\Card.tsx": {"lines": {"total": 88, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 88, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\Checkbox.tsx": {"lines": {"total": 83, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 83, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\CollapsibleSettingsCard.tsx": {"lines": {"total": 128, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 128, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\CoverageBar.tsx": {"lines": {"total": 196, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 196, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\CoverageModal.tsx": {"lines": {"total": 337, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 337, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\CoverageVisualization.tsx": {"lines": {"total": 352, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 352, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\GlassCrawlDepthSelector.tsx": {"lines": {"total": 157, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 157, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\Input.tsx": {"lines": {"total": 36, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 36, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\NeonButton.tsx": {"lines": {"total": 335, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 335, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\PowerButton.tsx": {"lines": {"total": 173, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 173, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\Select.tsx": {"lines": {"total": 47, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 47, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\TestResultDashboard.tsx": {"lines": {"total": 410, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 410, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\TestResultsModal.tsx": {"lines": {"total": 437, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 437, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\ThemeToggle.tsx": {"lines": {"total": 53, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 53, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\components\\ui\\Toggle.tsx": {"lines": {"total": 32, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 32, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\config\\api.ts": {"lines": {"total": 64, "covered": 54, "skipped": 0, "pct": 84.37}, "functions": {"total": 3, "covered": 2, "skipped": 0, "pct": 66.66}, "statements": {"total": 64, "covered": 54, "skipped": 0, "pct": 84.37}, "branches": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}}, "E:\\archon-turbo\\archon-ui-main\\src\\contexts\\SettingsContext.tsx": {"lines": {"total": 91, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 91, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\contexts\\ThemeContext.tsx": {"lines": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\contexts\\ToastContext.tsx": {"lines": {"total": 120, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 120, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\hooks\\useBugReport.ts": {"lines": {"total": 60, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 60, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\hooks\\useCardTilt.ts": {"lines": {"total": 92, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 92, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\hooks\\useNeonGlow.ts": {"lines": {"total": 203, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 203, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\hooks\\useOptimisticUpdates.ts": {"lines": {"total": 53, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 53, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\hooks\\useSocketSubscription.ts": {"lines": {"total": 37, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 37, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\hooks\\useStaggeredEntrance.ts": {"lines": {"total": 74, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 74, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\hooks\\useTaskSocket.ts": {"lines": {"total": 142, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 142, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\hooks\\useTerminalScroll.ts": {"lines": {"total": 74, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 74, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\lib\\projectSchemas.ts": {"lines": {"total": 195, "covered": 170, "skipped": 0, "pct": 87.17}, "functions": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 195, "covered": 170, "skipped": 0, "pct": 87.17}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "E:\\archon-turbo\\archon-ui-main\\src\\lib\\task-utils.tsx": {"lines": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\lib\\utils.ts": {"lines": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\pages\\KnowledgeBasePage.tsx": {"lines": {"total": 1542, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 1542, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\pages\\MCPPage.tsx": {"lines": {"total": 770, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 770, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\pages\\OnboardingPage.tsx": {"lines": {"total": 163, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 163, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\pages\\ProjectPage.tsx": {"lines": {"total": 1132, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 1132, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\pages\\SettingsPage.tsx": {"lines": {"total": 303, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 303, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\agentChatService.ts": {"lines": {"total": 741, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 741, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\api.ts": {"lines": {"total": 182, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 182, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\bugReportService.ts": {"lines": {"total": 244, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 244, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\crawlProgressService.ts": {"lines": {"total": 441, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 441, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\credentialsService.ts": {"lines": {"total": 384, "covered": 139, "skipped": 0, "pct": 36.19}, "functions": {"total": 12, "covered": 4, "skipped": 0, "pct": 33.33}, "statements": {"total": 384, "covered": 139, "skipped": 0, "pct": 36.19}, "branches": {"total": 10, "covered": 7, "skipped": 0, "pct": 70}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\knowledgeBaseService.ts": {"lines": {"total": 301, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 301, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\mcpClientService.ts": {"lines": {"total": 445, "covered": 250, "skipped": 0, "pct": 56.17}, "functions": {"total": 19, "covered": 3, "skipped": 0, "pct": 15.78}, "statements": {"total": 445, "covered": 250, "skipped": 0, "pct": 56.17}, "branches": {"total": 7, "covered": 5, "skipped": 0, "pct": 71.42}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\mcpServerService.ts": {"lines": {"total": 349, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 349, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\mcpService.ts": {"lines": {"total": 647, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 647, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\projectCreationProgressService.ts": {"lines": {"total": 170, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 170, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\projectService.ts": {"lines": {"total": 765, "covered": 315, "skipped": 0, "pct": 41.17}, "functions": {"total": 33, "covered": 7, "skipped": 0, "pct": 21.21}, "statements": {"total": 765, "covered": 315, "skipped": 0, "pct": 41.17}, "branches": {"total": 28, "covered": 22, "skipped": 0, "pct": 78.57}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\serverHealthService.ts": {"lines": {"total": 177, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 177, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\socketIOService.ts": {"lines": {"total": 494, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 494, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\socketService.ts": {"lines": {"total": 831, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 831, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\taskSocketService.ts": {"lines": {"total": 327, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 327, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\services\\testService.ts": {"lines": {"total": 437, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 437, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "E:\\archon-turbo\\archon-ui-main\\src\\types\\project.ts": {"lines": {"total": 205, "covered": 198, "skipped": 0, "pct": 96.58}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 205, "covered": 198, "skipped": 0, "pct": 96.58}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "E:\\archon-turbo\\archon-ui-main\\src\\utils\\onboarding.ts": {"lines": {"total": 108, "covered": 80, "skipped": 0, "pct": 74.07}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 108, "covered": 80, "skipped": 0, "pct": 74.07}, "branches": {"total": 28, "covered": 20, "skipped": 0, "pct": 71.42}}}