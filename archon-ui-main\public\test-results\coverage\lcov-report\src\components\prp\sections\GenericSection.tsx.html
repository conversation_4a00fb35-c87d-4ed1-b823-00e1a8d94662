
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/prp/sections/GenericSection.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">src/components/prp/sections</a> GenericSection.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/233</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/233</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >import React from 'react';</span></span></span>
<span class="cstat-no" title="statement not covered" >import { FileText, Hash, List, Box, Type, ToggleLeft } from 'lucide-react';</span>
<span class="cstat-no" title="statement not covered" >import { SectionProps } from '../types/prp.types';</span>
<span class="cstat-no" title="statement not covered" >import { formatKey, formatValue } from '../utils/formatters';</span>
<span class="cstat-no" title="statement not covered" >import { hasComplexNesting } from '../utils/normalizer';</span>
<span class="cstat-no" title="statement not covered" >import { CollapsibleSectionWrapper } from '../components/CollapsibleSectionWrapper';</span>
<span class="cstat-no" title="statement not covered" >import { SimpleMarkdown } from '../components/SimpleMarkdown';</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >/**</span>
<span class="cstat-no" title="statement not covered" > * Generic fallback section component that intelligently renders any data structure</span>
<span class="cstat-no" title="statement not covered" > * This component provides comprehensive rendering for any data type with proper formatting</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" >export const GenericSection: React.FC&lt;SectionProps&gt; = ({</span>
<span class="cstat-no" title="statement not covered" >  title,</span>
<span class="cstat-no" title="statement not covered" >  data,</span>
<span class="cstat-no" title="statement not covered" >  icon = &lt;FileText className="w-5 h-5" /&gt;,</span>
<span class="cstat-no" title="statement not covered" >  accentColor = 'gray',</span>
<span class="cstat-no" title="statement not covered" >  defaultOpen = true,</span>
<span class="cstat-no" title="statement not covered" >  isDarkMode = false,</span>
<span class="cstat-no" title="statement not covered" >  isCollapsible = true,</span>
<span class="cstat-no" title="statement not covered" >  isOpen,</span>
<span class="cstat-no" title="statement not covered" >  onToggle</span>
<span class="cstat-no" title="statement not covered" >}) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  // Auto-detect appropriate icon based on data type</span>
<span class="cstat-no" title="statement not covered" >  const getAutoIcon = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (typeof data === 'string') return &lt;Type className="w-5 h-5" /&gt;;</span>
<span class="cstat-no" title="statement not covered" >    if (typeof data === 'number') return &lt;Hash className="w-5 h-5" /&gt;;</span>
<span class="cstat-no" title="statement not covered" >    if (typeof data === 'boolean') return &lt;ToggleLeft className="w-5 h-5" /&gt;;</span>
<span class="cstat-no" title="statement not covered" >    if (Array.isArray(data)) return &lt;List className="w-5 h-5" /&gt;;</span>
<span class="cstat-no" title="statement not covered" >    if (typeof data === 'object' &amp;&amp; data !== null) return &lt;Box className="w-5 h-5" /&gt;;</span>
<span class="cstat-no" title="statement not covered" >    return icon;</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" >  const renderValue = (value: any, depth: number = 0): React.ReactNode =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const indent = depth * 16;</span>
<span class="cstat-no" title="statement not covered" >    const maxDepth = 5; // Prevent infinite recursion</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // Handle null/undefined</span>
<span class="cstat-no" title="statement not covered" >    if (value === null || value === undefined) {</span>
<span class="cstat-no" title="statement not covered" >      return &lt;span className="text-gray-400 italic"&gt;Empty&lt;/span&gt;;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // Handle primitives</span>
<span class="cstat-no" title="statement not covered" >    if (typeof value === 'string') {</span>
<span class="cstat-no" title="statement not covered" >      // Check if the string looks like markdown content</span>
<span class="cstat-no" title="statement not covered" >      const hasMarkdownIndicators = /^#{1,6}\s+.+$|^[-*+]\s+.+$|^\d+\.\s+.+$|```|^\&gt;.+$|\*\*.+\*\*|\*.+\*|`[^`]+`/m.test(value);</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      if (hasMarkdownIndicators &amp;&amp; value.length &gt; 20) {</span>
<span class="cstat-no" title="statement not covered" >        // Render as markdown for content with markdown syntax</span>
<span class="cstat-no" title="statement not covered" >        // Remove any leading headers since the section already has a title</span>
<span class="cstat-no" title="statement not covered" >        const contentWithoutLeadingHeaders = value.replace(/^#{1,6}\s+.+$/m, '').trim();</span>
<span class="cstat-no" title="statement not covered" >        const finalContent = contentWithoutLeadingHeaders || value;</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        return &lt;SimpleMarkdown content={finalContent} className="text-gray-700 dark:text-gray-300" /&gt;;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      // For shorter strings or non-markdown, use simple formatting</span>
<span class="cstat-no" title="statement not covered" >      return &lt;span className="text-gray-700 dark:text-gray-300"&gt;{formatValue(value)}&lt;/span&gt;;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    if (typeof value === 'number' || typeof value === 'boolean') {</span>
<span class="cstat-no" title="statement not covered" >      return &lt;span className="text-gray-700 dark:text-gray-300 font-mono"&gt;{formatValue(value)}&lt;/span&gt;;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // Prevent deep recursion</span>
<span class="cstat-no" title="statement not covered" >    if (depth &gt;= maxDepth) {</span>
<span class="cstat-no" title="statement not covered" >      return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;span className="text-gray-500 italic text-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >          [Complex nested structure - too deep to display]</span>
<span class="cstat-no" title="statement not covered" >        &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >      );</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // Handle arrays</span>
<span class="cstat-no" title="statement not covered" >    if (Array.isArray(value)) {</span>
<span class="cstat-no" title="statement not covered" >      if (value.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >        return &lt;span className="text-gray-400 italic"&gt;No items&lt;/span&gt;;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      // Check if it's an array of primitives</span>
<span class="cstat-no" title="statement not covered" >      const isSimpleArray = value.every(item =&gt; </span>
<span class="cstat-no" title="statement not covered" >        typeof item === 'string' || </span>
<span class="cstat-no" title="statement not covered" >        typeof item === 'number' || </span>
<span class="cstat-no" title="statement not covered" >        typeof item === 'boolean' ||</span>
<span class="cstat-no" title="statement not covered" >        item === null ||</span>
<span class="cstat-no" title="statement not covered" >        item === undefined</span>
<span class="cstat-no" title="statement not covered" >      );</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      if (isSimpleArray) {</span>
<span class="cstat-no" title="statement not covered" >        // For very long arrays, show first 10 and count</span>
<span class="cstat-no" title="statement not covered" >        const displayItems = value.length &gt; 10 ? value.slice(0, 10) : value;</span>
<span class="cstat-no" title="statement not covered" >        const hasMore = value.length &gt; 10;</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        return (</span>
<span class="cstat-no" title="statement not covered" >          &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;ul className="space-y-1 mt-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >              {displayItems.map((item, index) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                &lt;li key={index} className="flex items-start gap-2" style={{ marginLeft: indent }}&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="text-gray-400 mt-0.5"&gt;•&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="text-gray-700 dark:text-gray-300"&gt;{formatValue(item)}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >              ))}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/ul&gt;</span>
<span class="cstat-no" title="statement not covered" >            {hasMore &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >              &lt;p className="text-sm text-gray-500 italic mt-2" style={{ marginLeft: indent + 16 }}&gt;</span>
<span class="cstat-no" title="statement not covered" >                ... and {value.length - 10} more items</span>
<span class="cstat-no" title="statement not covered" >              &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            )}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        );</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      // Array of objects</span>
<span class="cstat-no" title="statement not covered" >      const displayItems = value.length &gt; 5 ? value.slice(0, 5) : value;</span>
<span class="cstat-no" title="statement not covered" >      const hasMore = value.length &gt; 5;</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="space-y-3 mt-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {displayItems.map((item, index) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >            &lt;div key={index} className="relative" style={{ marginLeft: indent }}&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="absolute left-0 top-0 bottom-0 w-0.5 bg-gradient-to-b from-gray-300 to-transparent dark:from-gray-600"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="pl-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  [{index}]</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                {renderValue(item, depth + 1)}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          ))}</span>
<span class="cstat-no" title="statement not covered" >          {hasMore &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >            &lt;p className="text-sm text-gray-500 italic" style={{ marginLeft: indent + 16 }}&gt;</span>
<span class="cstat-no" title="statement not covered" >              ... and {value.length - 5} more items</span>
<span class="cstat-no" title="statement not covered" >            &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >          )}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      );</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // Handle objects</span>
<span class="cstat-no" title="statement not covered" >    if (typeof value === 'object' &amp;&amp; value !== null) {</span>
<span class="cstat-no" title="statement not covered" >      // Simplified object rendering to debug black screen</span>
<span class="cstat-no" title="statement not covered" >      return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="mt-2 text-gray-700 dark:text-gray-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;pre className="text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded whitespace-pre-wrap"&gt;</span>
<span class="cstat-no" title="statement not covered" >            {JSON.stringify(value, null, 2)}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/pre&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      );</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // Fallback for any other type (functions, symbols, etc.)</span>
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >      &lt;span className="text-gray-500 italic text-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >        [{typeof value}]</span>
<span class="cstat-no" title="statement not covered" >      &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >    );</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  const getBackgroundColor = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const colorMap = {</span>
<span class="cstat-no" title="statement not covered" >      blue: 'bg-blue-50/50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800',</span>
<span class="cstat-no" title="statement not covered" >      purple: 'bg-purple-50/50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800',</span>
<span class="cstat-no" title="statement not covered" >      green: 'bg-green-50/50 dark:bg-green-900/20 border-green-200 dark:border-green-800',</span>
<span class="cstat-no" title="statement not covered" >      orange: 'bg-orange-50/50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800',</span>
<span class="cstat-no" title="statement not covered" >      pink: 'bg-pink-50/50 dark:bg-pink-900/20 border-pink-200 dark:border-pink-800',</span>
<span class="cstat-no" title="statement not covered" >      cyan: 'bg-cyan-50/50 dark:bg-cyan-900/20 border-cyan-200 dark:border-cyan-800',</span>
<span class="cstat-no" title="statement not covered" >      gray: 'bg-gray-50/50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800',</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" >    return colorMap[accentColor as keyof typeof colorMap] || colorMap.gray;</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  const finalIcon = icon === &lt;FileText className="w-5 h-5" /&gt; ? getAutoIcon() : icon;</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Enhanced styling based on data complexity</span>
<span class="cstat-no" title="statement not covered" >  const isComplexData = hasComplexNesting(data);</span>
<span class="cstat-no" title="statement not covered" >  const headerClass = isComplexData </span>
<span class="cstat-no" title="statement not covered" >    ? `p-6 rounded-lg border-2 shadow-sm ${getBackgroundColor()}`</span>
<span class="cstat-no" title="statement not covered" >    : `p-4 rounded-lg border ${getBackgroundColor()}`;</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  const header = (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className={headerClass}&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;h3 className="font-semibold text-gray-800 dark:text-white flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="p-1.5 rounded bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {finalIcon}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;span className="flex-1"&gt;{title}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  );</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  const contentClass = isComplexData </span>
<span class="cstat-no" title="statement not covered" >    ? `px-6 pb-6 -mt-1 rounded-b-lg border-2 border-t-0 shadow-sm ${getBackgroundColor()}`</span>
<span class="cstat-no" title="statement not covered" >    : `px-4 pb-4 -mt-1 rounded-b-lg border border-t-0 ${getBackgroundColor()}`;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  const content = (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className={contentClass}&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="overflow-x-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >        {/* Add a subtle background for complex data */}</span>
<span class="cstat-no" title="statement not covered" >        {isComplexData ? (</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="bg-gray-50 dark:bg-gray-900/50 rounded p-3 -mx-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >            {renderValue(data)}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        ) : (</span>
<span class="cstat-no" title="statement not covered" >          renderValue(data)</span>
<span class="cstat-no" title="statement not covered" >        )}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  );</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  try {</span>
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >      &lt;CollapsibleSectionWrapper</span>
<span class="cstat-no" title="statement not covered" >        header={header}</span>
<span class="cstat-no" title="statement not covered" >        isCollapsible={isCollapsible}</span>
<span class="cstat-no" title="statement not covered" >        defaultOpen={defaultOpen}</span>
<span class="cstat-no" title="statement not covered" >        isOpen={isOpen}</span>
<span class="cstat-no" title="statement not covered" >        onToggle={onToggle}</span>
<span class="cstat-no" title="statement not covered" >      &gt;</span>
<span class="cstat-no" title="statement not covered" >        {content}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/CollapsibleSectionWrapper&gt;</span>
<span class="cstat-no" title="statement not covered" >    );</span>
<span class="cstat-no" title="statement not covered" >  } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >    console.error('Error rendering GenericSection:', error, { title, data });</span>
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="p-4 border border-red-300 rounded bg-red-50 dark:bg-red-900"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h3 className="text-red-800 dark:text-red-200 font-semibold"&gt;{title}&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-red-600 dark:text-red-300 text-sm mt-2"&gt;Error rendering section content&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;pre className="text-xs mt-2 bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {JSON.stringify(data, null, 2)}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/pre&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    );</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >};</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-28T08:39:05.993Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    