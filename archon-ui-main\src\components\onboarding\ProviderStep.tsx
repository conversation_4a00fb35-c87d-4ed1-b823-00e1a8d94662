import { useState } from "react";
import { Key, ExternalLink, Save, Loader, Globe, Server } from "lucide-react";
import { Input } from "../ui/Input";
import { Button } from "../ui/Button";
import { useToast } from "../../contexts/ToastContext";
import { credentialsService } from "../../services/credentialsService";

interface ProviderStepProps {
  onSaved: () => void;
  onSkip: () => void;
}

type QuickTemplate = {
  name: string;
  chatModel: string;
  chatBaseUrl: string;
  chatApiKey: string;
  embeddingModel: string;
  embeddingBaseUrl: string;
  embeddingApiKey: string;
  description: string;
  requiresApiKey: boolean;
};

const quickTemplates: QuickTemplate[] = [
  {
    name: "OpenAI",
    chatModel: "gpt-4o-mini",
    chatBaseUrl: "https://api.openai.com/v1",
    chatApiKey: "OPENAI_API_KEY",
    embeddingModel: "text-embedding-3-small",
    embeddingBaseUrl: "https://api.openai.com/v1",
    embeddingApiKey: "OPENAI_API_KEY",
    description: "Official OpenAI models (GPT-4, GPT-3.5)",
    requiresApiKey: true,
  },
  {
    name: "Ollama (Local)",
    chatModel: "llama3.2",
    chatBaseUrl: "http://localhost:11434/v1",
    chatApiKey: "ollama",
    embeddingModel: "nomic-embed-text",
    embeddingBaseUrl: "http://localhost:11434/v1",
    embeddingApiKey: "ollama",
    description: "Run models locally on your machine",
    requiresApiKey: false,
  },
  {
    name: "Custom",
    chatModel: "",
    chatBaseUrl: "",
    chatApiKey: "",
    embeddingModel: "",
    embeddingBaseUrl: "",
    embeddingApiKey: "",
    description: "Configure your own OpenAI-compatible provider",
    requiresApiKey: true,
  },
];

export const ProviderStep = ({ onSaved, onSkip }: ProviderStepProps) => {
  const [selectedTemplate, setSelectedTemplate] = useState<QuickTemplate>(quickTemplates[0]);
  const [apiKey, setApiKey] = useState("");
  const [customBaseUrl, setCustomBaseUrl] = useState("");
  const [customModel, setCustomModel] = useState("");
  const [saving, setSaving] = useState(false);
  const { showToast } = useToast();

  const handleTemplateSelect = (template: QuickTemplate) => {
    setSelectedTemplate(template);
    if (template.name === "Custom") {
      setCustomBaseUrl("");
      setCustomModel("");
    }
  };

  const handleSave = async () => {
    // Validate required fields
    if (selectedTemplate.requiresApiKey && !apiKey.trim()) {
      showToast("Please enter an API key", "error");
      return;
    }

    if (selectedTemplate.name === "Custom") {
      if (!customBaseUrl.trim() || !customModel.trim()) {
        showToast("Please fill in all custom configuration fields", "error");
        return;
      }
    }

    setSaving(true);
    try {
      // Save API key if provided
      if (apiKey.trim() && selectedTemplate.requiresApiKey) {
        await credentialsService.createCredential({
          key: "OPENAI_API_KEY",
          value: apiKey,
          is_encrypted: true,
          category: "api_keys",
        });
      }

      // Prepare configuration based on template
      let chatConfig, embeddingConfig;
      
      if (selectedTemplate.name === "Custom") {
        // Use custom configuration for both chat and embedding
        chatConfig = {
          CHAT_MODEL: customModel,
          CHAT_BASE_URL: customBaseUrl,
          CHAT_API_KEY: "OPENAI_API_KEY",
        };
        embeddingConfig = {
          EMBEDDING_MODEL: customModel,
          EMBEDDING_BASE_URL: customBaseUrl,
          EMBEDDING_API_KEY: "OPENAI_API_KEY",
        };
      } else {
        // Use template configuration
        chatConfig = {
          CHAT_MODEL: selectedTemplate.chatModel,
          CHAT_BASE_URL: selectedTemplate.chatBaseUrl,
          CHAT_API_KEY: selectedTemplate.chatApiKey,
        };
        embeddingConfig = {
          EMBEDDING_MODEL: selectedTemplate.embeddingModel,
          EMBEDDING_BASE_URL: selectedTemplate.embeddingBaseUrl,
          EMBEDDING_API_KEY: selectedTemplate.embeddingApiKey,
        };
      }

      // Save configuration settings
      await credentialsService.updateRagSettings({
        ...chatConfig,
        ...embeddingConfig,
        // Keep legacy field for compatibility
        MODEL_CHOICE: chatConfig.CHAT_MODEL,
      });

      showToast("Configuration saved successfully!", "success");
      // Mark onboarding as dismissed
      localStorage.setItem("onboardingDismissed", "true");
      onSaved();
    } catch (error) {
      // Log error for debugging per alpha principles
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      console.error("Failed to save configuration:", error);

      // Show specific error details to help user resolve the issue
      if (
        errorMessage.includes("duplicate") ||
        errorMessage.includes("already exists")
      ) {
        showToast(
          "Configuration already exists. Please update it in Settings if you want to change it.",
          "warning",
        );
      } else if (
        errorMessage.includes("network") ||
        errorMessage.includes("fetch")
      ) {
        showToast(
          `Network error while saving configuration: ${errorMessage}. Please check your connection.`,
          "error",
        );
      } else {
        // Show the actual error for unknown issues
        showToast(`Failed to save configuration: ${errorMessage}`, "error");
      }
    } finally {
      setSaving(false);
    }
  };

  const handleSkip = () => {
    showToast("You can configure your AI provider in Settings", "info");
    // Mark onboarding as dismissed when skipping
    localStorage.setItem("onboardingDismissed", "true");
    onSkip();
  };

  return (
    <div className="space-y-6">
      {/* Quick Setup Templates */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700 dark:text-zinc-300">
          Quick Setup Templates
        </label>
        <div className="grid grid-cols-1 gap-3">
          {quickTemplates.map((template) => (
            <button
              key={template.name}
              onClick={() => handleTemplateSelect(template)}
              className={`p-4 rounded-lg border-2 transition-all text-left ${
                selectedTemplate.name === template.name
                  ? "border-green-500 bg-green-500/10 shadow-lg"
                  : "border-gray-200 dark:border-zinc-800 hover:border-green-500/50"
              }`}
            >
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    {template.name}
                  </h4>
                  <p className="mt-1 text-sm text-gray-600 dark:text-zinc-400">
                    {template.description}
                  </p>
                </div>
                {selectedTemplate.name === template.name && (
                  <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Configuration Details */}
      {selectedTemplate.requiresApiKey && (
        <div>
          <Input
            label={selectedTemplate.name === "OpenAI" ? "OpenAI API Key" : "API Key"}
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder={selectedTemplate.name === "OpenAI" ? "sk-..." : "Enter your API key"}
            accentColor="green"
            icon={<Key className="w-4 h-4" />}
          />
          <p className="mt-2 text-sm text-gray-600 dark:text-zinc-400">
            Your API key will be encrypted and stored securely.
          </p>
          {selectedTemplate.name === "OpenAI" && (
            <a
              href="https://platform.openai.com/api-keys"
              target="_blank"
              rel="noopener noreferrer"
              className="mt-2 inline-flex items-center gap-1 text-sm text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Get an API key from OpenAI
              <ExternalLink className="w-3 h-3" />
            </a>
          )}
        </div>
      )}

      {/* Custom Configuration */}
      {selectedTemplate.name === "Custom" && (
        <div className="space-y-4">
          <Input
            label="Base URL"
            value={customBaseUrl}
            onChange={(e) => setCustomBaseUrl(e.target.value)}
            placeholder="https://api.example.com/v1"
            accentColor="green"
            icon={<Globe className="w-4 h-4" />}
          />
          <Input
            label="Model Name"
            value={customModel}
            onChange={(e) => setCustomModel(e.target.value)}
            placeholder="gpt-4o-mini or llama3.2"
            accentColor="green"
            icon={<Server className="w-4 h-4" />}
          />
          <p className="text-sm text-gray-600 dark:text-zinc-400">
            Your provider must support the OpenAI API standard.
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-3 pt-4">
        <Button
          variant="primary"
          size="lg"
          onClick={handleSave}
          disabled={
            saving ||
            (selectedTemplate.requiresApiKey && !apiKey.trim()) ||
            (selectedTemplate.name === "Custom" && (!customBaseUrl.trim() || !customModel.trim()))
          }
          icon={
            saving ? (
              <Loader className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )
          }
          accentColor="green"
          className="flex-1"
        >
          {saving ? "Saving..." : "Save Configuration"}
        </Button>
        <Button
          variant="outline"
          size="lg"
          onClick={handleSkip}
          disabled={saving}
          accentColor="gray"
          className="flex-1"
        >
          Skip for Now
        </Button>
      </div>
    </div>
  );
};