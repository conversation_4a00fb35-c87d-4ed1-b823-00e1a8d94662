
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/animations/Animations.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/components/animations</a> Animations.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/118</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/118</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import React from 'react';<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >/**</span>
<span class="cstat-no" title="statement not covered" > * ArchonLoadingSpinner - A loading animation component with neon trail effects</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * This component displays the Archon logo with animated spinning circles</span>
<span class="cstat-no" title="statement not covered" > * that create a neon trail effect. It's used to indicate loading states</span>
<span class="cstat-no" title="statement not covered" > * throughout the application.</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * @param {Object} props - Component props</span>
<span class="cstat-no" title="statement not covered" > * @param {string} props.size - Size variant ('sm', 'md', 'lg')</span>
<span class="cstat-no" title="statement not covered" > * @param {string} props.logoSrc - Source URL for the logo image</span>
<span class="cstat-no" title="statement not covered" > * @param {string} props.className - Additional CSS classes</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" >export const ArchonLoadingSpinner: React.FC&lt;{</span>
<span class="cstat-no" title="statement not covered" >  size?: 'sm' | 'md' | 'lg';</span>
<span class="cstat-no" title="statement not covered" >  logoSrc?: string;</span>
<span class="cstat-no" title="statement not covered" >  className?: string;</span>
<span class="cstat-no" title="statement not covered" >}&gt; = ({</span>
<span class="cstat-no" title="statement not covered" >  size = 'md',</span>
<span class="cstat-no" title="statement not covered" >  logoSrc = "/logo-neon.png",</span>
<span class="cstat-no" title="statement not covered" >  className = ''</span>
<span class="cstat-no" title="statement not covered" >}) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  // Size mappings for the container and logo</span>
<span class="cstat-no" title="statement not covered" >  const sizeMap = {</span>
<span class="cstat-no" title="statement not covered" >    sm: {</span>
<span class="cstat-no" title="statement not covered" >      container: 'w-8 h-8',</span>
<span class="cstat-no" title="statement not covered" >      logo: 'w-5 h-5'</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    md: {</span>
<span class="cstat-no" title="statement not covered" >      container: 'w-10 h-10',</span>
<span class="cstat-no" title="statement not covered" >      logo: 'w-7 h-7'</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    lg: {</span>
<span class="cstat-no" title="statement not covered" >      container: 'w-14 h-14',</span>
<span class="cstat-no" title="statement not covered" >      logo: 'w-9 h-9'</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" >  return &lt;div className={`relative ${sizeMap[size].container} flex items-center justify-center ${className}`}&gt;</span>
<span class="cstat-no" title="statement not covered" >      {/* Central logo */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;img src={logoSrc} alt="Loading" className={`${sizeMap[size].logo} z-10 relative`} /&gt;</span>
<span class="cstat-no" title="statement not covered" >      {/* Animated spinning circles with neon trail effects */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="absolute inset-0 w-full h-full"&gt;</span>
<span class="cstat-no" title="statement not covered" >        {/* First circle - cyan with clockwise rotation */}</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="absolute inset-0 rounded-full border-2 border-transparent border-t-cyan-400 animate-[spin_0.8s_linear_infinite] blur-[0.5px] after:content-[''] after:absolute after:inset-0 after:rounded-full after:border-2 after:border-transparent after:border-t-cyan-400/30 after:blur-[3px] after:scale-110"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        {/* Second circle - fuchsia with counter-clockwise rotation */}</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="absolute inset-0 rounded-full border-2 border-transparent border-r-fuchsia-400 animate-[spin_0.6s_linear_infinite_reverse] blur-[0.5px] after:content-[''] after:absolute after:inset-0 after:rounded-full after:border-2 after:border-transparent after:border-r-fuchsia-400/30 after:blur-[3px] after:scale-110"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;;</span>
<span class="cstat-no" title="statement not covered" >};</span>
<span class="cstat-no" title="statement not covered" >/**</span>
<span class="cstat-no" title="statement not covered" > * NeonGlowEffect - A component that adds a neon glow effect to its children</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * This component creates a container with a neon glow effect in different colors.</span>
<span class="cstat-no" title="statement not covered" > * It's used for highlighting UI elements with a cyberpunk/neon aesthetic.</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * @param {Object} props - Component props</span>
<span class="cstat-no" title="statement not covered" > * @param {React.ReactNode} props.children - Child elements</span>
<span class="cstat-no" title="statement not covered" > * @param {string} props.color - Color variant ('cyan', 'fuchsia', 'blue', 'purple', 'green', 'pink')</span>
<span class="cstat-no" title="statement not covered" > * @param {string} props.intensity - Glow intensity ('low', 'medium', 'high')</span>
<span class="cstat-no" title="statement not covered" > * @param {string} props.className - Additional CSS classes</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" >export const NeonGlowEffect: React.FC&lt;{</span>
<span class="cstat-no" title="statement not covered" >  children: React.ReactNode;</span>
<span class="cstat-no" title="statement not covered" >  color?: 'cyan' | 'fuchsia' | 'blue' | 'purple' | 'green' | 'pink';</span>
<span class="cstat-no" title="statement not covered" >  intensity?: 'low' | 'medium' | 'high';</span>
<span class="cstat-no" title="statement not covered" >  className?: string;</span>
<span class="cstat-no" title="statement not covered" >}&gt; = ({</span>
<span class="cstat-no" title="statement not covered" >  children,</span>
<span class="cstat-no" title="statement not covered" >  color = 'blue',</span>
<span class="cstat-no" title="statement not covered" >  intensity = 'medium',</span>
<span class="cstat-no" title="statement not covered" >  className = ''</span>
<span class="cstat-no" title="statement not covered" >}) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  // Color mappings for different neon colors</span>
<span class="cstat-no" title="statement not covered" >  const colorMap = {</span>
<span class="cstat-no" title="statement not covered" >    cyan: 'border-cyan-400 shadow-cyan-400/50 dark:shadow-cyan-400/70',</span>
<span class="cstat-no" title="statement not covered" >    fuchsia: 'border-fuchsia-400 shadow-fuchsia-400/50 dark:shadow-fuchsia-400/70',</span>
<span class="cstat-no" title="statement not covered" >    blue: 'border-blue-400 shadow-blue-400/50 dark:shadow-blue-400/70',</span>
<span class="cstat-no" title="statement not covered" >    purple: 'border-purple-500 shadow-purple-500/50 dark:shadow-purple-500/70',</span>
<span class="cstat-no" title="statement not covered" >    green: 'border-emerald-500 shadow-emerald-500/50 dark:shadow-emerald-500/70',</span>
<span class="cstat-no" title="statement not covered" >    pink: 'border-pink-500 shadow-pink-500/50 dark:shadow-pink-500/70'</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" >  // Intensity mappings for glow strength</span>
<span class="cstat-no" title="statement not covered" >  const intensityMap = {</span>
<span class="cstat-no" title="statement not covered" >    low: 'shadow-[0_0_5px_0]',</span>
<span class="cstat-no" title="statement not covered" >    medium: 'shadow-[0_0_10px_1px]',</span>
<span class="cstat-no" title="statement not covered" >    high: 'shadow-[0_0_15px_2px]'</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" >  return &lt;div className={`relative ${className}`}&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className={`absolute inset-0 rounded-md border ${colorMap[color]} ${intensityMap[intensity]}`}&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="relative z-10"&gt;{children}&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;;</span>
<span class="cstat-no" title="statement not covered" >};</span>
<span class="cstat-no" title="statement not covered" >/**</span>
<span class="cstat-no" title="statement not covered" > * EdgeLitEffect - A component that adds an edge-lit glow effect</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * This component creates a thin glowing line at the top of a container,</span>
<span class="cstat-no" title="statement not covered" > * simulating the effect of edge lighting.</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * @param {Object} props - Component props</span>
<span class="cstat-no" title="statement not covered" > * @param {string} props.color - Color variant ('blue', 'purple', 'green', 'pink')</span>
<span class="cstat-no" title="statement not covered" > * @param {string} props.className - Additional CSS classes</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" >export const EdgeLitEffect: React.FC&lt;{</span>
<span class="cstat-no" title="statement not covered" >  color?: 'blue' | 'purple' | 'green' | 'pink';</span>
<span class="cstat-no" title="statement not covered" >  className?: string;</span>
<span class="cstat-no" title="statement not covered" >}&gt; = ({</span>
<span class="cstat-no" title="statement not covered" >  color = 'blue',</span>
<span class="cstat-no" title="statement not covered" >  className = ''</span>
<span class="cstat-no" title="statement not covered" >}) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  // Color mappings for different edge-lit colors</span>
<span class="cstat-no" title="statement not covered" >  const colorMap = {</span>
<span class="cstat-no" title="statement not covered" >    blue: 'bg-blue-500 shadow-[0_0_10px_2px_rgba(59,130,246,0.4)] dark:shadow-[0_0_20px_5px_rgba(59,130,246,0.7)]',</span>
<span class="cstat-no" title="statement not covered" >    purple: 'bg-purple-500 shadow-[0_0_10px_2px_rgba(168,85,247,0.4)] dark:shadow-[0_0_20px_5px_rgba(168,85,247,0.7)]',</span>
<span class="cstat-no" title="statement not covered" >    green: 'bg-emerald-500 shadow-[0_0_10px_2px_rgba(16,185,129,0.4)] dark:shadow-[0_0_20px_5px_rgba(16,185,129,0.7)]',</span>
<span class="cstat-no" title="statement not covered" >    pink: 'bg-pink-500 shadow-[0_0_10px_2px_rgba(236,72,153,0.4)] dark:shadow-[0_0_20px_5px_rgba(236,72,153,0.7)]'</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" >  return &lt;div className={`absolute top-0 left-0 w-full h-[2px] ${colorMap[color]} ${className}`}&gt;&lt;/div&gt;;</span>
<span class="cstat-no" title="statement not covered" >};</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-28T08:39:05.494Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    