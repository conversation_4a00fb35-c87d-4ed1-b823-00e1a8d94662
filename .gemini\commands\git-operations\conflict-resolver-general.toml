description = "Intelligently resolve all Git merge conflicts in the repository"

prompt = """
# General Git Conflict Resolver

You are an expert at resolving Git merge conflicts intelligently. Your task is to resolve all merge conflicts in the current repository.

## Step-by-step process:

1. **First, check the current git status to understand the situation**
   !{git status}

2. **Identify all files with merge conflicts**
   !{git diff --name-only --diff-filter=U}

3. **Understand the context and history**
   !{git log --oneline -n 20 --graph --all}
   !{gh pr list --state open --limit 5 2>/dev/null || echo "GitHub CLI not available"}

4. **For each conflicted file:**
   - Read and understand both versions (ours and theirs)
   - Understand the intent of both changes
   - Use the github cli if available
   - Think hard and plan how to resolve each conflict 
   - Resolve conflicts by intelligently combining both changes when possible
   - If changes are incompatible, prefer the version that:
     - Maintains backward compatibility
     - Has better test coverage
     - Follows the project's coding standards better
     - Is more performant
   - Remove all conflict markers (<<<<<<<, =======, >>>>>>>)

5. **After resolving each file, verify the syntax is correct**

6. **Run any relevant tests to ensure nothing is broken**

7. **Stage the resolved files**

8. **Provide a summary of all resolutions made**

## Important guidelines:

- NEVER just pick one side blindly - understand both changes
- Preserve the intent of both branches when possible
- Look for semantic conflicts (code that merges cleanly but breaks functionality)
- If unsure, explain the conflict and ask for guidance
- Always test after resolution if tests are available
- Consider the broader context of the codebase

## Commands you should use:

- `git status` - Check current state
- `git diff` - Understand changes
- `git log --oneline -n 20 --graph --all` - Understand recent history
- Read conflicted files to understand the conflicts
- Edit files to resolve conflicts
- `git add <file>` - Stage resolved files
- Run tests with appropriate commands (npm test, pytest, etc.)
- Use the github cli if available to check the PRs and understand the context and conflicts

## Resolution Process

Let me start by analyzing the current conflict situation:

!{git status}
!{git diff --name-only --diff-filter=U}

Now I'll proceed to resolve each conflict systematically, ensuring I understand the intent behind both changes and create a solution that preserves the best of both approaches.
"""