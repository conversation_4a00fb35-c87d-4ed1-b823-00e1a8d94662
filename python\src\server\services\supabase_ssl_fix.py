"""
SSL fix for custom Supabase domains with certificate hostname mismatch
"""

import ssl
import httpx
from typing import Optional


def create_ssl_context() -> ssl.SSLContext:
    """Create an SSL context that accepts the certificate despite hostname mismatch."""
    # Create a custom SSL context
    context = ssl.create_default_context()
    # Disable hostname verification
    context.check_hostname = False
    # But still verify the certificate chain (just not the hostname)
    context.verify_mode = ssl.CERT_REQUIRED
    return context


def create_httpx_client_with_ssl_fix() -> httpx.Client:
    """Create an httpx client with custom SSL handling for Supabase."""
    return httpx.Client(
        verify=create_ssl_context(),
        timeout=30.0,
    )


def create_async_httpx_client_with_ssl_fix() -> httpx.AsyncClient:
    """Create an async httpx client with custom SSL handling for Supabase."""
    return httpx.AsyncClient(
        verify=create_ssl_context(),
        timeout=30.0,
    )