-- OpenAI Unified Architecture Migration Script
-- Transforms multi-provider configuration to unified OpenAI-compatible structure
-- Author: <PERSON>
-- Date: 2025-08-28

-- Start transaction for atomic migration
BEGIN;

-- Create temporary function for safe JSO<PERSON> updates
CREATE OR REPLACE FUNCTION safe_json_set(
    json_data JSONB, 
    key_path TEXT[], 
    new_value JSONB
) RETURNS JSONB AS $$
BEGIN
    RETURN jsonb_set(
        COALESCE(json_data, '{}'::jsonb), 
        key_path, 
        new_value, 
        true
    );
END;
$$ LANGUAGE plpgsql;

-- Step 1: Backup current settings
CREATE TEMP TABLE archon_settings_backup AS
SELECT * FROM archon_settings WHERE category = 'rag_strategy';

-- Step 2: Add new configuration fields with intelligent migration
DO $$
DECLARE
    current_provider TEXT;
    current_model TEXT;
    current_embedding_model TEXT;
    current_api_key TEXT;
    current_base_url TEXT;
    new_settings JSONB;
    setting_record RECORD;
BEGIN
    -- Process each rag_strategy setting
    FOR setting_record IN 
        SELECT * FROM archon_settings 
        WHERE category = 'rag_strategy'
    LOOP
        -- Extract current configuration
        current_provider := setting_record.settings->>'LLM_PROVIDER';
        current_model := setting_record.settings->>'LLM_MODEL';
        current_embedding_model := setting_record.settings->>'EMBEDDING_MODEL';
        
        -- Initialize new settings from existing ones
        new_settings := setting_record.settings;
        
        -- Migrate based on current provider
        IF current_provider = 'openai' OR current_provider IS NULL THEN
            -- OpenAI migration
            new_settings := safe_json_set(new_settings, '{CHAT_MODEL}', 
                COALESCE(to_jsonb(current_model), '"gpt-4o-mini"'::jsonb));
            new_settings := safe_json_set(new_settings, '{CHAT_API_KEY}', 
                '"OPENAI_API_KEY"'::jsonb);
            new_settings := safe_json_set(new_settings, '{CHAT_BASE_URL}', 
                '"https://api.openai.com/v1"'::jsonb);
            new_settings := safe_json_set(new_settings, '{EMBEDDING_MODEL}', 
                COALESCE(to_jsonb(current_embedding_model), '"text-embedding-3-small"'::jsonb));
            new_settings := safe_json_set(new_settings, '{EMBEDDING_API_KEY}', 
                '"OPENAI_API_KEY"'::jsonb);
            new_settings := safe_json_set(new_settings, '{EMBEDDING_BASE_URL}', 
                '"https://api.openai.com/v1"'::jsonb);
                
        ELSIF current_provider = 'ollama' THEN
            -- Ollama migration
            current_base_url := COALESCE(
                setting_record.settings->>'LLM_BASE_URL',
                'http://localhost:11434/v1'
            );
            new_settings := safe_json_set(new_settings, '{CHAT_MODEL}', 
                COALESCE(to_jsonb(current_model), '"llama3.2"'::jsonb));
            new_settings := safe_json_set(new_settings, '{CHAT_API_KEY}', 
                '"ollama"'::jsonb);  -- Dummy key required by OpenAI client
            new_settings := safe_json_set(new_settings, '{CHAT_BASE_URL}', 
                to_jsonb(current_base_url));
            new_settings := safe_json_set(new_settings, '{EMBEDDING_MODEL}', 
                COALESCE(to_jsonb(current_embedding_model), '"nomic-embed-text"'::jsonb));
            new_settings := safe_json_set(new_settings, '{EMBEDDING_API_KEY}', 
                '"ollama"'::jsonb);  -- Dummy key required by OpenAI client
            new_settings := safe_json_set(new_settings, '{EMBEDDING_BASE_URL}', 
                to_jsonb(current_base_url));
                
        ELSIF current_provider = 'google' THEN
            -- Google Gemini migration (Note: Requires proxy adapter)
            new_settings := safe_json_set(new_settings, '{CHAT_MODEL}', 
                COALESCE(to_jsonb(current_model), '"gemini-1.5-flash"'::jsonb));
            new_settings := safe_json_set(new_settings, '{CHAT_API_KEY}', 
                '"GOOGLE_API_KEY"'::jsonb);
            new_settings := safe_json_set(new_settings, '{CHAT_BASE_URL}', 
                '"https://generativelanguage.googleapis.com/v1beta/openai/"'::jsonb);
            new_settings := safe_json_set(new_settings, '{EMBEDDING_MODEL}', 
                COALESCE(to_jsonb(current_embedding_model), '"text-embedding-004"'::jsonb));
            new_settings := safe_json_set(new_settings, '{EMBEDDING_API_KEY}', 
                '"GOOGLE_API_KEY"'::jsonb);
            new_settings := safe_json_set(new_settings, '{EMBEDDING_BASE_URL}', 
                '"https://generativelanguage.googleapis.com/v1beta/openai/"'::jsonb);
            
            -- Add migration notice for Google users
            new_settings := safe_json_set(new_settings, '{MIGRATION_NOTICE}',
                '"Google Gemini support requires a proxy adapter for OpenAI compatibility"'::jsonb);
        END IF;
        
        -- Remove old provider-specific fields
        new_settings := new_settings - 'LLM_PROVIDER';
        new_settings := new_settings - 'LLM_BASE_URL';
        -- Keep LLM_MODEL and EMBEDDING_MODEL for backward compatibility during transition
        
        -- Update the record with new settings
        UPDATE archon_settings 
        SET 
            settings = new_settings,
            updated = CURRENT_TIMESTAMP
        WHERE id = setting_record.id;
    END LOOP;
END $$;

-- Step 3: Migrate credentials table if it exists
DO $$
BEGIN
    -- Check if credentials table exists
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'credentials'
    ) THEN
        -- Update any Google API key references to maintain compatibility
        UPDATE credentials 
        SET description = 'API key for OpenAI-compatible endpoints (migrated from Google)'
        WHERE name = 'GOOGLE_API_KEY';
    END IF;
END $$;

-- Step 4: Create validation view to verify migration
CREATE OR REPLACE VIEW migration_validation AS
SELECT 
    id,
    settings->>'CHAT_MODEL' as chat_model,
    settings->>'CHAT_API_KEY' as chat_api_key,
    settings->>'CHAT_BASE_URL' as chat_base_url,
    settings->>'EMBEDDING_MODEL' as embedding_model,
    settings->>'EMBEDDING_API_KEY' as embedding_api_key,
    settings->>'EMBEDDING_BASE_URL' as embedding_base_url,
    settings->>'MIGRATION_NOTICE' as migration_notice
FROM archon_settings 
WHERE category = 'rag_strategy';

-- Step 5: Validation checks
DO $$
DECLARE
    missing_configs INTEGER;
    backup_count INTEGER;
    migrated_count INTEGER;
BEGIN
    -- Count records missing required configurations
    SELECT COUNT(*) INTO missing_configs
    FROM archon_settings
    WHERE category = 'rag_strategy'
    AND (
        settings->>'CHAT_MODEL' IS NULL OR
        settings->>'CHAT_API_KEY' IS NULL OR
        settings->>'CHAT_BASE_URL' IS NULL OR
        settings->>'EMBEDDING_MODEL' IS NULL OR
        settings->>'EMBEDDING_API_KEY' IS NULL OR
        settings->>'EMBEDDING_BASE_URL' IS NULL
    );
    
    -- Count backup records
    SELECT COUNT(*) INTO backup_count FROM archon_settings_backup;
    
    -- Count migrated records
    SELECT COUNT(*) INTO migrated_count 
    FROM archon_settings 
    WHERE category = 'rag_strategy';
    
    -- Validate migration
    IF missing_configs > 0 THEN
        RAISE EXCEPTION 'Migration validation failed: % records missing required configurations', missing_configs;
    END IF;
    
    IF backup_count != migrated_count THEN
        RAISE EXCEPTION 'Migration validation failed: backup count (%) does not match migrated count (%)', 
            backup_count, migrated_count;
    END IF;
    
    RAISE NOTICE 'Migration successful: % records migrated', migrated_count;
END $$;

-- Step 6: Create helper functions for the new structure
CREATE OR REPLACE FUNCTION get_chat_config(user_id UUID DEFAULT NULL)
RETURNS JSONB AS $$
DECLARE
    config JSONB;
BEGIN
    SELECT jsonb_build_object(
        'model', settings->>'CHAT_MODEL',
        'api_key', settings->>'CHAT_API_KEY',
        'base_url', settings->>'CHAT_BASE_URL'
    ) INTO config
    FROM archon_settings
    WHERE category = 'rag_strategy'
    AND (owner_id = user_id OR owner_id IS NULL)
    ORDER BY owner_id DESC NULLS LAST
    LIMIT 1;
    
    RETURN config;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_embedding_config(user_id UUID DEFAULT NULL)
RETURNS JSONB AS $$
DECLARE
    config JSONB;
BEGIN
    SELECT jsonb_build_object(
        'model', settings->>'EMBEDDING_MODEL',
        'api_key', settings->>'EMBEDDING_API_KEY',
        'base_url', settings->>'EMBEDDING_BASE_URL'
    ) INTO config
    FROM archon_settings
    WHERE category = 'rag_strategy'
    AND (owner_id = user_id OR owner_id IS NULL)
    ORDER BY owner_id DESC NULLS LAST
    LIMIT 1;
    
    RETURN config;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Clean up temporary objects
DROP FUNCTION safe_json_set(JSONB, TEXT[], JSONB);

-- Commit the transaction
COMMIT;

-- Step 8: Post-migration report
SELECT 
    'Migration Complete' as status,
    COUNT(*) as records_migrated,
    COUNT(DISTINCT settings->>'CHAT_BASE_URL') as unique_chat_endpoints,
    COUNT(DISTINCT settings->>'EMBEDDING_BASE_URL') as unique_embedding_endpoints,
    STRING_AGG(DISTINCT settings->>'MIGRATION_NOTICE', '; ') as migration_notices
FROM archon_settings 
WHERE category = 'rag_strategy';

-- Display migration results
SELECT * FROM migration_validation;

-- Optional: Rollback procedure (run only if needed)
-- BEGIN;
-- DELETE FROM archon_settings WHERE category = 'rag_strategy';
-- INSERT INTO archon_settings SELECT * FROM archon_settings_backup;
-- DROP TABLE archon_settings_backup;
-- DROP VIEW migration_validation;
-- DROP FUNCTION get_chat_config(UUID);
-- DROP FUNCTION get_embedding_config(UUID);
-- COMMIT;