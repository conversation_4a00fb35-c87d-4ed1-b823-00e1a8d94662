description = "Execute specification PRP with systematic transformation"

prompt = """
# Execute SPEC PRP

Implement a specification using an existing SPEC PRP.

## PRP File: $ARGUMENTS

## Execution Process

1. **Understand Spec**
   - Read the SPEC PRP: @PRPs/$ARGUMENTS
   - Current state analysis
   - Desired state goals
   - Task dependencies

2. **ULTRATHINK**
   - Think hard before you execute the plan. Create a comprehensive plan addressing all requirements.
   - Break down complex tasks into smaller, manageable steps using your todos tools.
   - Use the TodoWrite tool to create and track your implementation plan.
   - Identify implementation patterns from existing code to follow.

3. **Execute Tasks**
   - Follow task order as specified in the SPEC PRP
   - Run validation after each task completion
   - Fix failures before proceeding to next task
   - Document progress and any deviations

4. **Verify Transformation**
   - Confirm desired state achieved
   - Run all validation gates specified in the SPEC PRP
   - Test integration points
   - Verify no regressions introduced

## Implementation Guidelines

- Follow the hierarchical objectives from high-level to low-level
- Use information-dense keywords (MODIFY, CREATE, ADD, etc.) as specified
- Maintain rollback capability at each step
- Apply progressive enhancement approach
- Document any assumptions or decisions made during implementation

## Validation Protocol

For each task in the SPEC PRP:
1. Execute the specified changes
2. Run the validation command
3. Verify expected results
4. Fix any issues before proceeding
5. Update task status

## Progress Tracking

Use TodoWrite tool to track:
- [ ] SPEC PRP analysis completed
- [ ] Implementation plan created
- [ ] Each transformation task (as listed in SPEC PRP)
- [ ] Validation gates passed
- [ ] Integration testing completed
- [ ] Final verification successful

Progress through each objective systematically, ensuring the transformation meets all specified requirements.
"""