# Plano de Refatoração: Compatibilidade Universal com OpenAI API

## Visão Geral

Este plano detalha a modificação da aplicação Archon para ser compatível apenas com provedores que seguem o padrão da OpenAI API. O usuário poderá configurar independentemente:

- **Chat Model**: model, api_key, base_url
- **Embedding Model**: model, api_key, base_url

## Análise da Estrutura Atual

### Componentes Identificados

1. **Backend (Python)**
   - `credential_service.py` - Gerencia credenciais e configurações
   - `llm_provider_service.py` - Cria clientes LLM específicos por provedor
   - `internal_api.py` - Fornece credenciais para agentes
   - Agentes (`document_agent.py`, `rag_agent.py`) - Usam modelos específicos

2. **Frontend (React/TypeScript)**
   - `ProviderStep.tsx` - Onboarding com seleção de provedor
   - `RAGSettings.tsx` - Configurações de RAG e modelos
   - `APIKeysSection.tsx` - Gerenciamento de chaves API
   - `credentialsService.ts` - Interface para API de credenciais

3. **Banco de Dados**
   - Tabela `archon_settings` - Armazena configurações e credenciais
   - Configurações atuais: `LLM_PROVIDER`, `LLM_BASE_URL`, `EMBEDDING_MODEL`

4. **Testes**
   - `test_async_llm_provider_service.py` - Testa diferentes provedores
   - `test_async_embedding_service.py` - Testa serviços de embedding

## Objetivos da Refatoração

### 1. Simplificação da Arquitetura
- Remover lógica específica para Google Gemini e Ollama
- Manter apenas cliente OpenAI universal
- Eliminar mapeamentos de provedores específicos

### 2. Configuração Independente
- Chat Model: `CHAT_MODEL`, `CHAT_API_KEY`, `CHAT_BASE_URL`
- Embedding Model: `EMBEDDING_MODEL`, `EMBEDDING_API_KEY`, `EMBEDDING_BASE_URL`

### 3. Interface Unificada
- UI simplificada para configuração de dois conjuntos independentes
- Remoção de seleção de provedor específico
- Foco em configuração de endpoint OpenAI-compatível

## Fases de Implementação

### Fase 1: Modificação do Backend

#### 1.1 Atualização do Schema de Banco de Dados
- Criar nova migração para adicionar campos:
  - `CHAT_MODEL`, `CHAT_API_KEY`, `CHAT_BASE_URL`
  - `EMBEDDING_MODEL`, `EMBEDDING_API_KEY`, `EMBEDDING_BASE_URL`
- Migrar dados existentes para novo formato
- Remover campos obsoletos: `LLM_PROVIDER`

#### 1.2 Refatoração do Credential Service
- Modificar `get_active_provider()` para retornar configurações separadas
- Adicionar métodos `get_chat_config()` e `get_embedding_config()`
- Remover lógica específica de provedores
- Atualizar cache para novos campos

#### 1.3 Simplificação do LLM Provider Service
- Remover condicionais para Google e Ollama
- Manter apenas criação de cliente OpenAI universal
- Usar configurações independentes para chat e embedding
- Simplificar função `get_llm_client()`

#### 1.4 Atualização dos Agentes
- Modificar agentes para usar novas configurações
- Atualizar `internal_api.py` para fornecer credenciais corretas
- Remover referências a provedores específicos

### Fase 2: Modificação do Frontend

#### 2.1 Refatoração do Onboarding
- Simplificar `ProviderStep.tsx` para configuração OpenAI-compatível
- Remover seleção de provedor específico
- Adicionar campos para base URL personalizada

#### 2.2 Atualização das Configurações
- Modificar `RAGSettings.tsx` para duas seções independentes:
  - Chat Model Configuration
  - Embedding Model Configuration
- Cada seção com: Model, API Key, Base URL
- Remover dropdown de provedor

#### 2.3 Atualização do Serviço de Credenciais
- Modificar `credentialsService.ts` para novos campos
- Atualizar interfaces TypeScript
- Adaptar métodos de get/set para nova estrutura

### Fase 3: Limpeza e Testes

#### 3.1 Remoção de Código Legado
- Remover todas as referências a Google Gemini
- Remover todas as referências a Ollama
- Limpar imports e dependências não utilizadas
- Remover arquivos de configuração específicos

#### 3.2 Atualização de Testes
- Modificar testes para nova estrutura
- Remover testes específicos de provedores
- Adicionar testes para configuração independente
- Validar compatibilidade com diferentes endpoints OpenAI

#### 3.3 Atualização de Documentação
- Atualizar README com nova configuração
- Modificar documentação da API
- Atualizar exemplos de configuração

## Estrutura de Configuração Proposta

### Configurações de Chat Model
```
CHAT_MODEL=gpt-4o-mini
CHAT_API_KEY=sk-...
CHAT_BASE_URL=https://api.openai.com/v1
```

### Configurações de Embedding Model
```
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_API_KEY=sk-...
EMBEDDING_BASE_URL=https://api.openai.com/v1
```

### Exemplos de Provedores Compatíveis
- **OpenAI**: `https://api.openai.com/v1`
- **Azure OpenAI**: `https://your-resource.openai.azure.com/`
- **Ollama**: `http://localhost:11434/v1`
- **LM Studio**: `http://localhost:1234/v1`
- **vLLM**: `http://your-server:8000/v1`
- **Together AI**: `https://api.together.xyz/v1`
- **Groq**: `https://api.groq.com/openai/v1`

## Benefícios da Refatoração

### 1. Simplicidade
- Código mais limpo e maintível
- Menos condicionais e mapeamentos
- Arquitetura unificada

### 2. Flexibilidade
- Suporte a qualquer provedor OpenAI-compatível
- Configuração independente de chat e embedding
- Facilita adição de novos provedores compatíveis

### 3. Manutenibilidade
- Menos código para manter
- Testes mais simples
- Debugging mais fácil

## Riscos e Mitigações

### 1. Compatibilidade com Dados Existentes
- **Risco**: Perda de configurações existentes
- **Mitigação**: Script de migração com validação
  - Detectar provider atual e migrar adequadamente
  - Validar integridade dos dados após migração
  - Usar transações SQL para garantir atomicidade

### 2. Interrupção do Serviço
- **Risco**: Downtime durante migração
- **Mitigação**: Deploy gradual
  - Usar transações SQL para atomicidade
  - Rollback automático em caso de erro na migração

### 3. Configuração Incorreta
- **Risco**: Usuários configurarem endpoints incorretos
- **Mitigação**: Validação de endpoints e documentação clara
  - Implementar função de validação de endpoint OpenAI-compatível
  - Testar conectividade antes de salvar configuração
  - Fornecer exemplos para provedores populares

## Cronograma Estimado

- **Fase 1 (Backend)**: 4-5 dias - Inclui validação de endpoints e sistema de cache
- **Fase 2 (Frontend)**: 2-3 dias - Interface para configuração dual
- **Fase 3 (Limpeza e Testes)**: 2-3 dias - Testes abrangentes e documentação
- **Total**: 8-11 dias - Maior robustez e confiabilidade

## Detalhamento Técnico

### Modificações Específicas por Arquivo

#### Backend (Python)

**1. migration/openai_unified_migration.sql**
```sql
-- Script de migração com validação
BEGIN; -- Transação para atomicidade

-- Detectar e validar configuração atual
DO $$
DECLARE
    current_provider TEXT;
    migration_chat_api_key TEXT;
    migration_chat_base_url TEXT;
BEGIN
    SELECT value INTO current_provider FROM archon_settings WHERE key = 'LLM_PROVIDER';
    
    -- Migração inteligente baseada no provider
    IF current_provider = 'openai' THEN
        -- OpenAI: migração direta
        SELECT encrypted_value INTO migration_chat_api_key 
        FROM archon_settings WHERE key = 'OPENAI_API_KEY';
        migration_chat_base_url := 'https://api.openai.com/v1';
        
    ELSIF current_provider = 'ollama' THEN
        -- Ollama: usar base URL customizada
        SELECT value INTO migration_chat_base_url 
        FROM archon_settings WHERE key = 'LLM_BASE_URL';
        migration_chat_base_url := COALESCE(migration_chat_base_url, 'http://localhost:11434/v1');
        
    ELSIF current_provider = 'google' THEN
        -- Google: avisar sobre necessidade de configuração manual
        RAISE WARNING 'Google Gemini detected - manual configuration required';
    END IF;
    
    -- Inserir novas configurações
    INSERT INTO archon_settings (key, value, encrypted_value, is_encrypted, category, description)
    VALUES 
        ('CHAT_MODEL', (SELECT value FROM archon_settings WHERE key = 'MODEL_CHOICE'), 
         NULL, false, 'llm_config', 'Chat model name'),
        ('CHAT_API_KEY', NULL, migration_chat_api_key, true, 'llm_config', 'API key for chat model'),
        ('CHAT_BASE_URL', migration_chat_base_url, NULL, false, 'llm_config', 'Base URL for chat API');
END $$;

-- Remover configurações antigas
DELETE FROM archon_settings WHERE key IN ('LLM_PROVIDER', 'LLM_BASE_URL', 'MODEL_CHOICE');

-- Validação final
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM archon_settings WHERE key IN ('CHAT_MODEL', 'CHAT_API_KEY', 'CHAT_BASE_URL')) THEN
        RAISE EXCEPTION 'Migration validation failed';
    END IF;
END $$;

COMMIT;
```

**2. credential_service.py - Novos métodos com validação**
```python
async def get_chat_config(self) -> dict[str, Any]:
    """Get chat model configuration."""
    config = await self.get_credentials_by_category("llm_config")
    return {
        "model": config.get("CHAT_MODEL", "gpt-4o-mini"),
        "api_key": await self.get_credential("CHAT_API_KEY", decrypt=True),
        "base_url": config.get("CHAT_BASE_URL", "https://api.openai.com/v1"),
    }

async def get_embedding_config(self) -> dict[str, Any]:
    """Get embedding model configuration."""
    config = await self.get_credentials_by_category("embedding_config")
    return {
        "model": config.get("EMBEDDING_MODEL", "text-embedding-3-small"),
        "api_key": await self.get_credential("EMBEDDING_API_KEY", decrypt=True),
        "base_url": config.get("EMBEDDING_BASE_URL", "https://api.openai.com/v1"),
    }

def invalidate_cache(self) -> None:
    """Clear all cached settings when configuration changes."""
    self._cache.clear()
    self._cache_initialized = False
    self._rag_settings_cache = None
    self._rag_cache_timestamp = None
    logger.debug("Configuration cache invalidated")

async def validate_openai_endpoint(self, base_url: str, api_key: str) -> tuple[bool, str]:
    """Validate if endpoint is OpenAI API compatible."""
    try:
        client = openai.AsyncOpenAI(api_key=api_key, base_url=base_url, timeout=10.0)
        models = await asyncio.wait_for(client.models.list(), timeout=10.0)
        model_count = len(list(models.data)) if hasattr(models, 'data') else 0
        return True, f"Endpoint valid. Found {model_count} models."
    except openai.AuthenticationError:
        return False, "Invalid API key"
    except openai.APIConnectionError as e:
        return False, f"Connection failed: {str(e)}"
    except asyncio.TimeoutError:
        return False, "Connection timeout - endpoint not responding"
    except Exception as e:
        return False, f"Validation failed: {str(e)}"
```

**3. llm_provider_service.py - Simplificação com cache melhorado**
```python
# Cache global com TTL
_config_cache: dict[str, tuple[Any, float]] = {}
_CACHE_TTL_SECONDS = 300  # 5 minutos

def invalidate_provider_cache():
    """Clear provider configuration cache."""
    global _config_cache
    _config_cache.clear()
    logger.debug("Provider cache invalidated")

@asynccontextmanager
async def get_llm_client(use_embedding_config: bool = False):
    """Create OpenAI-compatible client with caching."""
    try:
        # Check cache first
        cache_key = "embedding" if use_embedding_config else "chat"
        cached = _config_cache.get(cache_key)
        
        if cached and time.time() - cached[1] < _CACHE_TTL_SECONDS:
            config = cached[0]
        else:
            # Fetch new config
            if use_embedding_config:
                config = await credential_service.get_embedding_config()
            else:
                config = await credential_service.get_chat_config()
            
            # Update cache
            _config_cache[cache_key] = (config, time.time())

        if not config.get("api_key"):
            raise ValueError(f"API key not found for {cache_key} configuration")

        client = openai.AsyncOpenAI(
            api_key=config["api_key"],
            base_url=config["base_url"],
            timeout=30.0  # 30 second timeout for operations
        )
        
        logger.info(f"Created OpenAI client for {cache_key} with base_url: {config['base_url']}")
        yield client
        
    except Exception as e:
        logger.error(f"Error creating LLM client: {e}")
        raise
    finally:
        # Cleanup if needed
        pass

async def get_embedding_model() -> str:
    """Get the configured embedding model name."""
    config = await credential_service.get_embedding_config()
    return config["model"]
```

#### Frontend (React/TypeScript)

**4. RAGSettings.tsx - Nova estrutura**
```tsx
interface ModelConfig {
  model: string;
  apiKey: string;
  baseUrl: string;
}

interface RAGSettingsProps {
  chatConfig: ModelConfig;
  embeddingConfig: ModelConfig;
  setChatConfig: (config: ModelConfig) => void;
  setEmbeddingConfig: (config: ModelConfig) => void;
}

// Duas seções independentes:
// - Chat Model Configuration
// - Embedding Model Configuration
```

**5. credentialsService.ts - Novos métodos**
```typescript
async getChatConfig(): Promise<ModelConfig> {
  const config = await this.getCredentialsByCategory('llm_config');
  return {
    model: config.CHAT_MODEL || 'gpt-4o-mini',
    apiKey: await this.getCredential('CHAT_API_KEY'),
    baseUrl: config.CHAT_BASE_URL || 'https://api.openai.com/v1'
  };
}

async getEmbeddingConfig(): Promise<ModelConfig> {
  const config = await this.getCredentialsByCategory('embedding_config');
  return {
    model: config.EMBEDDING_MODEL || 'text-embedding-3-small',
    apiKey: await this.getCredential('EMBEDDING_API_KEY'),
    baseUrl: config.EMBEDDING_BASE_URL || 'https://api.openai.com/v1'
  };
}
```

### Arquivos a Serem Removidos/Modificados

#### Remover Completamente
- Referências específicas a Google Gemini
- Referências específicas a Ollama
- Lógica de mapeamento de provedores

#### Modificar Significativamente
- `python/src/server/services/llm_provider_service.py`
- `python/src/server/services/credential_service.py`
- `archon-ui-main/src/components/settings/RAGSettings.tsx`
- `archon-ui-main/src/components/onboarding/ProviderStep.tsx`
- `python/tests/test_async_llm_provider_service.py`

### Validações Necessárias

#### Backend
1. Validar formato de URL base
2. Testar conectividade com endpoint
3. Validar formato de API key
4. Verificar compatibilidade do modelo

#### Frontend
1. Validação de URL em tempo real
2. Teste de conectividade opcional
3. Validação de formato de chave API
4. Preview de configuração

### Casos de Teste

#### Cenários de Configuração
1. **OpenAI Padrão**: URLs padrão, modelos padrão
2. **Azure OpenAI**: URLs customizadas, autenticação específica
3. **Ollama Local**: localhost:11434, sem API key real
4. **Provedores Terceiros**: Together AI, Groq, etc.

#### Cenários de Migração
1. **Usuário OpenAI Existente**: Migração automática
2. **Usuário Google Existente**: Aviso e orientações para migração manual
3. **Usuário Ollama Existente**: Configuração de base URL preservada

#### Testes de Integração
```python
# tests/test_openai_compatible_providers.py
import pytest
from unittest.mock import patch, AsyncMock

@pytest.mark.parametrize("provider_config", [
    {"name": "OpenAI", "base_url": "https://api.openai.com/v1", "has_key": True},
    {"name": "Azure", "base_url": "https://your.openai.azure.com/", "has_key": True},
    {"name": "Ollama", "base_url": "http://localhost:11434/v1", "has_key": False},
    {"name": "Together", "base_url": "https://api.together.xyz/v1", "has_key": True},
    {"name": "Groq", "base_url": "https://api.groq.com/openai/v1", "has_key": True},
])
async def test_provider_compatibility(provider_config):
    """Test that various OpenAI-compatible providers work correctly."""
    from src.server.services.llm_provider_service import get_llm_client
    
    # Mock configuration
    mock_config = {
        "api_key": "test-key" if provider_config["has_key"] else None,
        "base_url": provider_config["base_url"],
        "model": "test-model"
    }
    
    with patch('credential_service.get_chat_config', return_value=mock_config):
        async with get_llm_client() as client:
            assert client is not None
            assert client.base_url == provider_config["base_url"]

async def test_endpoint_validation():
    """Test endpoint validation function."""
    from src.server.services.credential_service import credential_service
    
    # Test valid endpoint (mock)
    with patch('openai.AsyncOpenAI') as mock_client:
        mock_instance = AsyncMock()
        mock_instance.models.list.return_value = AsyncMock(data=[])
        mock_client.return_value = mock_instance
        
        is_valid, message = await credential_service.validate_openai_endpoint(
            "https://api.openai.com/v1", "test-key"
        )
        assert is_valid is True
        assert "valid" in message.lower()
    
    # Test invalid endpoint
    is_valid, message = await credential_service.validate_openai_endpoint(
        "https://invalid.endpoint.com", "test-key"
    )
    assert is_valid is False
    assert "failed" in message.lower()

async def test_cache_invalidation():
    """Test that cache is properly invalidated on config change."""
    from src.server.services.credential_service import credential_service
    from src.server.services.llm_provider_service import invalidate_provider_cache
    
    # Set initial config
    await credential_service.set_chat_config("model1", "key1", "url1")
    
    # Get config (should cache)
    config1 = await credential_service.get_chat_config()
    
    # Invalidate cache
    credential_service.invalidate_cache()
    invalidate_provider_cache()
    
    # Set new config
    await credential_service.set_chat_config("model2", "key2", "url2")
    
    # Get config again (should not use cache)
    config2 = await credential_service.get_chat_config()
    
    assert config1["model"] != config2["model"]
```

## Próximos Passos

1. Aprovação do plano
2. Criação de branch para desenvolvimento
3. Implementação da Fase 1 (Backend)
4. Testes da Fase 1
5. Implementação da Fase 2 (Frontend)
6. Testes da Fase 2
7. Implementação da Fase 3 (Limpeza)
8. Testes finais e deploy

## Guia de Migração para Usuários

### Para usuários OpenAI
Sua configuração será migrada automaticamente. Apenas verifique após a atualização.

### Para usuários Ollama
1. Sua base URL será preservada (padrão: `http://localhost:11434/v1`)
2. Configure o modelo correto para sua instalação local
3. API key pode ser qualquer string (Ollama não valida)

### Para usuários Google Gemini
Como o Google Gemini não segue o padrão OpenAI API, você precisará:

**Opção 1: Migrar para OpenAI**
```
CHAT_MODEL=gpt-4o-mini
CHAT_API_KEY=sk-...
CHAT_BASE_URL=https://api.openai.com/v1
```

**Opção 2: Usar um proxy/adapter**
- [LiteLLM Proxy](https://github.com/BerriAI/litellm) - Converte Gemini para OpenAI API
- Configure o proxy e use sua URL como base_url

**Opção 3: Migrar para alternativas compatíveis**
- **Groq**: Fast inference, OpenAI-compatible
- **Together AI**: Multiple models, OpenAI-compatible
- **Perplexity**: Search-enhanced models

### Exemplos de Configuração para Provedores Populares

**Azure OpenAI**
```
CHAT_BASE_URL=https://your-resource.openai.azure.com/openai/deployments/your-deployment-name
CHAT_API_KEY=your-azure-api-key
CHAT_MODEL=gpt-4
```

**LM Studio (Local)**
```
CHAT_BASE_URL=http://localhost:1234/v1
CHAT_API_KEY=not-needed
CHAT_MODEL=local-model-name
```

**Together AI**
```
CHAT_BASE_URL=https://api.together.xyz/v1
CHAT_API_KEY=your-together-key
CHAT_MODEL=meta-llama/Llama-3-70b-chat-hf
```

**Groq**
```
CHAT_BASE_URL=https://api.groq.com/openai/v1
CHAT_API_KEY=your-groq-key
CHAT_MODEL=llama-3.1-70b-versatile
```

## Monitoramento Pós-Migração

### Métricas a Acompanhar
1. **Taxa de sucesso de chamadas API** - Verificar compatibilidade real
2. **Tempo de resposta** - Comparar com provider anterior
3. **Erros de autenticação** - Identificar configurações incorretas
4. **Uso de cache** - Verificar efetividade do cache

### Logs Importantes
```python
# Adicionar logs detalhados durante período de transição
logger.info(f"Migration: Provider {old_provider} -> OpenAI-compatible")
logger.info(f"Endpoint validation: {base_url} - {validation_result}")
logger.info(f"Cache hit rate: {cache_hits}/{total_requests}")
```
