
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/ui/NeonButton.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/components/ui</a> NeonButton.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/335</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/335</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >import React from 'react';</span></span></span>
<span class="cstat-no" title="statement not covered" >import { motion, HTMLMotionProps } from 'framer-motion';</span>
<span class="cstat-no" title="statement not covered" >import { cn } from '../../lib/utils';</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >export interface CornerRadius {</span>
<span class="cstat-no" title="statement not covered" >  topLeft?: number;</span>
<span class="cstat-no" title="statement not covered" >  topRight?: number;</span>
<span class="cstat-no" title="statement not covered" >  bottomRight?: number;</span>
<span class="cstat-no" title="statement not covered" >  bottomLeft?: number;</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >export type GlowIntensity = 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';</span>
<span class="cstat-no" title="statement not covered" >export type ColorOption = 'none' | 'purple' | 'pink' | 'blue' | 'green' | 'red';</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >export interface NeonButtonProps extends Omit&lt;HTMLMotionProps&lt;'button'&gt;, 'children'&gt; {</span>
<span class="cstat-no" title="statement not covered" >  children: React.ReactNode;</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Layer controls</span>
<span class="cstat-no" title="statement not covered" >  showLayer2?: boolean;</span>
<span class="cstat-no" title="statement not covered" >  layer2Inset?: number; // Inset in pixels, can be negative for overlap</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Colors</span>
<span class="cstat-no" title="statement not covered" >  layer1Color?: ColorOption;</span>
<span class="cstat-no" title="statement not covered" >  layer2Color?: ColorOption;</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Corner radius per layer</span>
<span class="cstat-no" title="statement not covered" >  layer1Radius?: CornerRadius;</span>
<span class="cstat-no" title="statement not covered" >  layer2Radius?: CornerRadius;</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Glow controls</span>
<span class="cstat-no" title="statement not covered" >  layer1Glow?: GlowIntensity;</span>
<span class="cstat-no" title="statement not covered" >  layer2Glow?: GlowIntensity;</span>
<span class="cstat-no" title="statement not covered" >  borderGlow?: GlowIntensity;</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Border controls</span>
<span class="cstat-no" title="statement not covered" >  layer1Border?: boolean;</span>
<span class="cstat-no" title="statement not covered" >  layer2Border?: boolean;</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Text controls</span>
<span class="cstat-no" title="statement not covered" >  coloredText?: boolean; // Whether text takes on the button color</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Size</span>
<span class="cstat-no" title="statement not covered" >  size?: 'sm' | 'md' | 'lg' | 'xl';</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Basic states</span>
<span class="cstat-no" title="statement not covered" >  disabled?: boolean;</span>
<span class="cstat-no" title="statement not covered" >  fullWidth?: boolean;</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >export const NeonButton = React.forwardRef&lt;HTMLButtonElement, NeonButtonProps&gt;(({</span>
<span class="cstat-no" title="statement not covered" >  children,</span>
<span class="cstat-no" title="statement not covered" >  showLayer2 = false,</span>
<span class="cstat-no" title="statement not covered" >  layer2Inset = 8,</span>
<span class="cstat-no" title="statement not covered" >  layer1Color = 'none',</span>
<span class="cstat-no" title="statement not covered" >  layer2Color = 'none',</span>
<span class="cstat-no" title="statement not covered" >  layer1Radius = { topLeft: 12, topRight: 12, bottomRight: 12, bottomLeft: 12 },</span>
<span class="cstat-no" title="statement not covered" >  layer2Radius = { topLeft: 24, topRight: 24, bottomRight: 24, bottomLeft: 24 },</span>
<span class="cstat-no" title="statement not covered" >  layer1Glow = 'md',</span>
<span class="cstat-no" title="statement not covered" >  layer2Glow = 'md',</span>
<span class="cstat-no" title="statement not covered" >  borderGlow = 'none',</span>
<span class="cstat-no" title="statement not covered" >  layer1Border = true,</span>
<span class="cstat-no" title="statement not covered" >  layer2Border = true,</span>
<span class="cstat-no" title="statement not covered" >  coloredText = false,</span>
<span class="cstat-no" title="statement not covered" >  size = 'md',</span>
<span class="cstat-no" title="statement not covered" >  disabled = false,</span>
<span class="cstat-no" title="statement not covered" >  fullWidth = false,</span>
<span class="cstat-no" title="statement not covered" >  className,</span>
<span class="cstat-no" title="statement not covered" >  ...props</span>
<span class="cstat-no" title="statement not covered" >}, ref) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  // Size mappings</span>
<span class="cstat-no" title="statement not covered" >  const sizeClasses = {</span>
<span class="cstat-no" title="statement not covered" >    sm: 'px-3 py-1.5',</span>
<span class="cstat-no" title="statement not covered" >    md: 'px-4 py-2',</span>
<span class="cstat-no" title="statement not covered" >    lg: 'px-6 py-3',</span>
<span class="cstat-no" title="statement not covered" >    xl: 'px-8 py-4'</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  const textSizeClasses = {</span>
<span class="cstat-no" title="statement not covered" >    sm: 'text-sm',</span>
<span class="cstat-no" title="statement not covered" >    md: 'text-base',</span>
<span class="cstat-no" title="statement not covered" >    lg: 'text-lg',</span>
<span class="cstat-no" title="statement not covered" >    xl: 'text-xl'</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  // Glow intensity mappings</span>
<span class="cstat-no" title="statement not covered" >  const glowSizes = {</span>
<span class="cstat-no" title="statement not covered" >    none: { blur: 0, spread: 0, opacity: 0 },</span>
<span class="cstat-no" title="statement not covered" >    sm: { blur: 10, spread: 5, opacity: 0.3 },</span>
<span class="cstat-no" title="statement not covered" >    md: { blur: 15, spread: 10, opacity: 0.4 },</span>
<span class="cstat-no" title="statement not covered" >    lg: { blur: 20, spread: 15, opacity: 0.5 },</span>
<span class="cstat-no" title="statement not covered" >    xl: { blur: 30, spread: 20, opacity: 0.6 },</span>
<span class="cstat-no" title="statement not covered" >    xxl: { blur: 40, spread: 30, opacity: 0.7 }</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  // Convert radius object to style</span>
<span class="cstat-no" title="statement not covered" >  const getRadiusStyle = (radius: CornerRadius) =&gt; ({</span>
<span class="cstat-no" title="statement not covered" >    borderTopLeftRadius: `${radius.topLeft || 0}px`,</span>
<span class="cstat-no" title="statement not covered" >    borderTopRightRadius: `${radius.topRight || 0}px`,</span>
<span class="cstat-no" title="statement not covered" >    borderBottomRightRadius: `${radius.bottomRight || 0}px`,</span>
<span class="cstat-no" title="statement not covered" >    borderBottomLeftRadius: `${radius.bottomLeft || 0}px`,</span>
<span class="cstat-no" title="statement not covered" >  });</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  // Color mappings for gradients and borders</span>
<span class="cstat-no" title="statement not covered" >  const getColorConfig = (color: ColorOption) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const configs = {</span>
<span class="cstat-no" title="statement not covered" >      none: {</span>
<span class="cstat-no" title="statement not covered" >        border: 'border-white/20',</span>
<span class="cstat-no" title="statement not covered" >        glow: 'rgba(255,255,255,0.4)',</span>
<span class="cstat-no" title="statement not covered" >        glowDark: 'rgba(255,255,255,0.3)',</span>
<span class="cstat-no" title="statement not covered" >        aurora: 'rgba(255,255,255,0.4)',</span>
<span class="cstat-no" title="statement not covered" >        auroraDark: 'rgba(255,255,255,0.2)',</span>
<span class="cstat-no" title="statement not covered" >        text: 'rgb(156 163 175)', // gray-400</span>
<span class="cstat-no" title="statement not covered" >        textRgb: '156, 163, 175'</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      purple: {</span>
<span class="cstat-no" title="statement not covered" >        border: 'border-purple-400/30',</span>
<span class="cstat-no" title="statement not covered" >        glow: 'rgba(168,85,247,0.6)',</span>
<span class="cstat-no" title="statement not covered" >        glowDark: 'rgba(168,85,247,0.5)',</span>
<span class="cstat-no" title="statement not covered" >        aurora: 'rgba(168,85,247,0.8)',</span>
<span class="cstat-no" title="statement not covered" >        auroraDark: 'rgba(147,51,234,0.6)',</span>
<span class="cstat-no" title="statement not covered" >        text: 'rgb(168 85 247)', // purple-500</span>
<span class="cstat-no" title="statement not covered" >        textRgb: '168, 85, 247'</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      pink: {</span>
<span class="cstat-no" title="statement not covered" >        border: 'border-pink-400/30',</span>
<span class="cstat-no" title="statement not covered" >        glow: 'rgba(236,72,153,0.6)',</span>
<span class="cstat-no" title="statement not covered" >        glowDark: 'rgba(236,72,153,0.5)',</span>
<span class="cstat-no" title="statement not covered" >        aurora: 'rgba(236,72,153,0.8)',</span>
<span class="cstat-no" title="statement not covered" >        auroraDark: 'rgba(219,39,119,0.6)',</span>
<span class="cstat-no" title="statement not covered" >        text: 'rgb(236 72 153)', // pink-500</span>
<span class="cstat-no" title="statement not covered" >        textRgb: '236, 72, 153'</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      blue: {</span>
<span class="cstat-no" title="statement not covered" >        border: 'border-blue-400/30',</span>
<span class="cstat-no" title="statement not covered" >        glow: 'rgba(59,130,246,0.6)',</span>
<span class="cstat-no" title="statement not covered" >        glowDark: 'rgba(59,130,246,0.5)',</span>
<span class="cstat-no" title="statement not covered" >        aurora: 'rgba(59,130,246,0.8)',</span>
<span class="cstat-no" title="statement not covered" >        auroraDark: 'rgba(37,99,235,0.6)',</span>
<span class="cstat-no" title="statement not covered" >        text: 'rgb(59 130 246)', // blue-500</span>
<span class="cstat-no" title="statement not covered" >        textRgb: '59, 130, 246'</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      green: {</span>
<span class="cstat-no" title="statement not covered" >        border: 'border-green-400/30',</span>
<span class="cstat-no" title="statement not covered" >        glow: 'rgba(34,197,94,0.6)',</span>
<span class="cstat-no" title="statement not covered" >        glowDark: 'rgba(34,197,94,0.5)',</span>
<span class="cstat-no" title="statement not covered" >        aurora: 'rgba(34,197,94,0.8)',</span>
<span class="cstat-no" title="statement not covered" >        auroraDark: 'rgba(22,163,74,0.6)',</span>
<span class="cstat-no" title="statement not covered" >        text: 'rgb(34 197 94)', // green-500</span>
<span class="cstat-no" title="statement not covered" >        textRgb: '34, 197, 94'</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      red: {</span>
<span class="cstat-no" title="statement not covered" >        border: 'border-red-400/30',</span>
<span class="cstat-no" title="statement not covered" >        glow: 'rgba(239,68,68,0.6)',</span>
<span class="cstat-no" title="statement not covered" >        glowDark: 'rgba(239,68,68,0.5)',</span>
<span class="cstat-no" title="statement not covered" >        aurora: 'rgba(239,68,68,0.8)',</span>
<span class="cstat-no" title="statement not covered" >        auroraDark: 'rgba(220,38,38,0.6)',</span>
<span class="cstat-no" title="statement not covered" >        text: 'rgb(239 68 68)', // red-500</span>
<span class="cstat-no" title="statement not covered" >        textRgb: '239, 68, 68'</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" >    return configs[color];</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  const layer1Config = getColorConfig(layer1Color);</span>
<span class="cstat-no" title="statement not covered" >  const layer2Config = getColorConfig(layer2Color);</span>
<span class="cstat-no" title="statement not covered" >  const layer1GlowConfig = glowSizes[layer1Glow];</span>
<span class="cstat-no" title="statement not covered" >  const layer2GlowConfig = glowSizes[layer2Glow];</span>
<span class="cstat-no" title="statement not covered" >  const borderGlowConfig = glowSizes[borderGlow];</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  // Build box shadow</span>
<span class="cstat-no" title="statement not covered" >  const buildBoxShadow = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const shadows = [];</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // Layer 1 external glow</span>
<span class="cstat-no" title="statement not covered" >    if (layer1Glow !== 'none' &amp;&amp; !disabled) {</span>
<span class="cstat-no" title="statement not covered" >      shadows.push(`0 0 ${layer1GlowConfig.blur}px ${layer1Config.glow}`);</span>
<span class="cstat-no" title="statement not covered" >      shadows.push(`0 0 ${layer1GlowConfig.spread}px ${layer1Config.glowDark}`);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // Border glow</span>
<span class="cstat-no" title="statement not covered" >    if (borderGlow !== 'none' &amp;&amp; layer1Border &amp;&amp; !disabled) {</span>
<span class="cstat-no" title="statement not covered" >      shadows.push(`inset 0 0 ${borderGlowConfig.blur}px ${layer1Config.glow}`);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    return shadows.length &gt; 0 ? shadows.join(', ') : undefined;</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;motion.button</span>
<span class="cstat-no" title="statement not covered" >      ref={ref}</span>
<span class="cstat-no" title="statement not covered" >      disabled={disabled}</span>
<span class="cstat-no" title="statement not covered" >      className={cn(</span>
<span class="cstat-no" title="statement not covered" >        'relative overflow-hidden transition-all duration-300 group',</span>
<span class="cstat-no" title="statement not covered" >        sizeClasses[size],</span>
<span class="cstat-no" title="statement not covered" >        fullWidth &amp;&amp; 'w-full',</span>
<span class="cstat-no" title="statement not covered" >        disabled &amp;&amp; 'opacity-50 cursor-not-allowed',</span>
<span class="cstat-no" title="statement not covered" >        className</span>
<span class="cstat-no" title="statement not covered" >      )}</span>
<span class="cstat-no" title="statement not covered" >      whileHover={!disabled ? { scale: 1.02 } : {}}</span>
<span class="cstat-no" title="statement not covered" >      whileTap={!disabled ? { scale: 0.98 } : {}}</span>
<span class="cstat-no" title="statement not covered" >      style={{</span>
<span class="cstat-no" title="statement not covered" >        ...getRadiusStyle(layer1Radius),</span>
<span class="cstat-no" title="statement not covered" >        boxShadow: buildBoxShadow(),</span>
<span class="cstat-no" title="statement not covered" >      }}</span>
<span class="cstat-no" title="statement not covered" >      {...props}</span>
<span class="cstat-no" title="statement not covered" >    &gt;</span>
<span class="cstat-no" title="statement not covered" >      {/* Layer 1 - Main glass layer (opaque black glass) */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="relative w-full h-full" style={getRadiusStyle(layer1Radius)}&gt;</span>
<span class="cstat-no" title="statement not covered" >        {/* Border glow behind the glass */}</span>
<span class="cstat-no" title="statement not covered" >        {layer1Border &amp;&amp; layer1Glow !== 'none' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >          &lt;div </span>
<span class="cstat-no" title="statement not covered" >            className="absolute inset-0"</span>
<span class="cstat-no" title="statement not covered" >            style={{</span>
<span class="cstat-no" title="statement not covered" >              ...getRadiusStyle(layer1Radius),</span>
<span class="cstat-no" title="statement not covered" >              boxShadow: `0 0 ${layer1GlowConfig.blur}px ${layer1Config.glow}, 0 0 ${layer1GlowConfig.spread}px ${layer1Config.glowDark}`,</span>
<span class="cstat-no" title="statement not covered" >            }}</span>
<span class="cstat-no" title="statement not covered" >          /&gt;</span>
<span class="cstat-no" title="statement not covered" >        )}</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        {/* Glass surface */}</span>
<span class="cstat-no" title="statement not covered" >        &lt;div</span>
<span class="cstat-no" title="statement not covered" >          className={cn(</span>
<span class="cstat-no" title="statement not covered" >            'absolute inset-0',</span>
<span class="cstat-no" title="statement not covered" >            layer1Color === 'none' </span>
<span class="cstat-no" title="statement not covered" >              ? 'bg-white/90 dark:bg-black/90' </span>
<span class="cstat-no" title="statement not covered" >              : 'bg-white/90 dark:bg-black/90',</span>
<span class="cstat-no" title="statement not covered" >            'backdrop-blur-md',</span>
<span class="cstat-no" title="statement not covered" >            layer1Border &amp;&amp; `border ${layer1Config.border}`,</span>
<span class="cstat-no" title="statement not covered" >            'transition-all duration-300'</span>
<span class="cstat-no" title="statement not covered" >          )}</span>
<span class="cstat-no" title="statement not covered" >          style={getRadiusStyle(layer1Radius)}</span>
<span class="cstat-no" title="statement not covered" >        &gt;</span>
<span class="cstat-no" title="statement not covered" >          {/* Aurora glow effect for Layer 1 */}</span>
<span class="cstat-no" title="statement not covered" >          {layer1Color !== 'none' &amp;&amp; layer1Glow !== 'none' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >            &lt;div </span>
<span class="cstat-no" title="statement not covered" >              className="absolute inset-0 -z-10"</span>
<span class="cstat-no" title="statement not covered" >              style={{</span>
<span class="cstat-no" title="statement not covered" >                ...getRadiusStyle(layer1Radius),</span>
<span class="cstat-no" title="statement not covered" >                opacity: layer1GlowConfig.opacity</span>
<span class="cstat-no" title="statement not covered" >              }}</span>
<span class="cstat-no" title="statement not covered" >            &gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div </span>
<span class="cstat-no" title="statement not covered" >                className="absolute -inset-[100px] blur-3xl animate-[pulse_4s_ease-in-out_infinite]"</span>
<span class="cstat-no" title="statement not covered" >                style={{</span>
<span class="cstat-no" title="statement not covered" >                  background: `radial-gradient(circle, ${layer1Config.aurora} 0%, ${layer1Config.auroraDark} 40%, transparent 70%)`</span>
<span class="cstat-no" title="statement not covered" >                }}</span>
<span class="cstat-no" title="statement not covered" >              /&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          )}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >      {/* Layer 2 - Inner glass pill (optional) */}</span>
<span class="cstat-no" title="statement not covered" >      {showLayer2 &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div </span>
<span class="cstat-no" title="statement not covered" >          className="absolute pointer-events-none"</span>
<span class="cstat-no" title="statement not covered" >          style={{</span>
<span class="cstat-no" title="statement not covered" >            top: `${layer2Inset}px`,</span>
<span class="cstat-no" title="statement not covered" >            left: `${layer2Inset}px`,</span>
<span class="cstat-no" title="statement not covered" >            right: `${layer2Inset}px`,</span>
<span class="cstat-no" title="statement not covered" >            bottom: `${layer2Inset}px`</span>
<span class="cstat-no" title="statement not covered" >          }}</span>
<span class="cstat-no" title="statement not covered" >        &gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div</span>
<span class="cstat-no" title="statement not covered" >            className={cn(</span>
<span class="cstat-no" title="statement not covered" >              'relative w-full h-full backdrop-blur-sm',</span>
<span class="cstat-no" title="statement not covered" >              layer2Color === 'none' </span>
<span class="cstat-no" title="statement not covered" >                ? 'bg-gradient-to-b from-white/20 to-white/10 dark:from-white/20 dark:to-black/20' </span>
<span class="cstat-no" title="statement not covered" >                : layer2Color === 'purple'</span>
<span class="cstat-no" title="statement not covered" >                  ? 'bg-gradient-to-b from-purple-500/30 to-purple-600/30'</span>
<span class="cstat-no" title="statement not covered" >                  : layer2Color === 'pink'</span>
<span class="cstat-no" title="statement not covered" >                    ? 'bg-gradient-to-b from-pink-500/30 to-pink-600/30'</span>
<span class="cstat-no" title="statement not covered" >                    : layer2Color === 'blue'</span>
<span class="cstat-no" title="statement not covered" >                      ? 'bg-gradient-to-b from-blue-500/30 to-blue-600/30'</span>
<span class="cstat-no" title="statement not covered" >                      : layer2Color === 'green'</span>
<span class="cstat-no" title="statement not covered" >                        ? 'bg-gradient-to-b from-green-500/30 to-green-600/30'</span>
<span class="cstat-no" title="statement not covered" >                        : 'bg-gradient-to-b from-red-500/30 to-red-600/30',</span>
<span class="cstat-no" title="statement not covered" >              layer2Border &amp;&amp; `border ${layer2Config.border}`,</span>
<span class="cstat-no" title="statement not covered" >              'transition-all duration-300'</span>
<span class="cstat-no" title="statement not covered" >            )}</span>
<span class="cstat-no" title="statement not covered" >            style={{</span>
<span class="cstat-no" title="statement not covered" >              ...getRadiusStyle(layer2Radius),</span>
<span class="cstat-no" title="statement not covered" >              boxShadow: layer2Glow !== 'none' </span>
<span class="cstat-no" title="statement not covered" >                ? `0 0 ${layer2GlowConfig.blur}px ${layer2Config.glow}, 0 0 ${layer2GlowConfig.spread}px ${layer2Config.glowDark}` </span>
<span class="cstat-no" title="statement not covered" >                : undefined,</span>
<span class="cstat-no" title="statement not covered" >            }}</span>
<span class="cstat-no" title="statement not covered" >          &gt;</span>
<span class="cstat-no" title="statement not covered" >            {/* Aurora glow for Layer 2 that shines on Layer 1 */}</span>
<span class="cstat-no" title="statement not covered" >            {layer2Color !== 'none' &amp;&amp; layer2Glow !== 'none' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >              &lt;div </span>
<span class="cstat-no" title="statement not covered" >                className="absolute inset-0"</span>
<span class="cstat-no" title="statement not covered" >                style={{</span>
<span class="cstat-no" title="statement not covered" >                  ...getRadiusStyle(layer2Radius),</span>
<span class="cstat-no" title="statement not covered" >                  opacity: layer2GlowConfig.opacity</span>
<span class="cstat-no" title="statement not covered" >                }}</span>
<span class="cstat-no" title="statement not covered" >              &gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div </span>
<span class="cstat-no" title="statement not covered" >                  className="absolute -inset-[50px] blur-2xl animate-[pulse_6s_ease-in-out_infinite]"</span>
<span class="cstat-no" title="statement not covered" >                  style={{</span>
<span class="cstat-no" title="statement not covered" >                    background: `radial-gradient(circle, ${layer2Config.aurora} 0%, ${layer2Config.auroraDark} 30%, transparent 60%)`</span>
<span class="cstat-no" title="statement not covered" >                  }}</span>
<span class="cstat-no" title="statement not covered" >                /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            )}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      )}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >      {/* Text content - translucent to let color shine through */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;span </span>
<span class="cstat-no" title="statement not covered" >        className={cn(</span>
<span class="cstat-no" title="statement not covered" >          'relative z-10 font-medium',</span>
<span class="cstat-no" title="statement not covered" >          textSizeClasses[size],</span>
<span class="cstat-no" title="statement not covered" >          !coloredText &amp;&amp; 'mix-blend-overlay dark:mix-blend-screen'</span>
<span class="cstat-no" title="statement not covered" >        )}</span>
<span class="cstat-no" title="statement not covered" >        style={{</span>
<span class="cstat-no" title="statement not covered" >          color: coloredText </span>
<span class="cstat-no" title="statement not covered" >            ? (showLayer2 &amp;&amp; layer2Color !== 'none' </span>
<span class="cstat-no" title="statement not covered" >                ? layer2Config.text </span>
<span class="cstat-no" title="statement not covered" >                : layer1Color !== 'none' </span>
<span class="cstat-no" title="statement not covered" >                  ? layer1Config.text </span>
<span class="cstat-no" title="statement not covered" >                  : 'rgba(255, 255, 255, 0.8)')</span>
<span class="cstat-no" title="statement not covered" >            : 'rgba(255, 255, 255, 0.8)',</span>
<span class="cstat-no" title="statement not covered" >          textShadow: coloredText &amp;&amp; ((showLayer2 &amp;&amp; layer2Color !== 'none') || (!showLayer2 &amp;&amp; layer1Color !== 'none'))</span>
<span class="cstat-no" title="statement not covered" >            ? '0 1px 2px rgba(0,0,0,0.8)'</span>
<span class="cstat-no" title="statement not covered" >            : undefined</span>
<span class="cstat-no" title="statement not covered" >        }}</span>
<span class="cstat-no" title="statement not covered" >      &gt;</span>
<span class="cstat-no" title="statement not covered" >        {children}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/motion.button&gt;</span>
<span class="cstat-no" title="statement not covered" >  );</span>
<span class="cstat-no" title="statement not covered" >});</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >NeonButton.displayName = 'NeonButton'; </span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-28T08:39:05.993Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    