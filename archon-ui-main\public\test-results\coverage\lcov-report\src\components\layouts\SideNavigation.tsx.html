
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/layouts/SideNavigation.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/components/layouts</a> SideNavigation.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/130</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/130</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >import React, { useState } from 'react';</span></span></span>
<span class="cstat-no" title="statement not covered" >import { Link, useLocation } from 'react-router-dom';</span>
<span class="cstat-no" title="statement not covered" >import { BookOpen, HardDrive, Settings } from 'lucide-react';</span>
<span class="cstat-no" title="statement not covered" >import { useSettings } from '../../contexts/SettingsContext';</span>
<span class="cstat-no" title="statement not covered" >/**</span>
<span class="cstat-no" title="statement not covered" > * Interface for navigation items</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" >export interface NavigationItem {</span>
<span class="cstat-no" title="statement not covered" >  path: string;</span>
<span class="cstat-no" title="statement not covered" >  icon: React.ReactNode;</span>
<span class="cstat-no" title="statement not covered" >  label: string;</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" >/**</span>
<span class="cstat-no" title="statement not covered" > * Props for the SideNavigation component</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" >interface SideNavigationProps {</span>
<span class="cstat-no" title="statement not covered" >  className?: string;</span>
<span class="cstat-no" title="statement not covered" >  'data-id'?: string;</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" >/**</span>
<span class="cstat-no" title="statement not covered" > * Tooltip component for navigation items</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" >const NavTooltip: React.FC&lt;{</span>
<span class="cstat-no" title="statement not covered" >  show: boolean;</span>
<span class="cstat-no" title="statement not covered" >  label: string;</span>
<span class="cstat-no" title="statement not covered" >  position?: 'left' | 'right';</span>
<span class="cstat-no" title="statement not covered" >}&gt; = ({</span>
<span class="cstat-no" title="statement not covered" >  show,</span>
<span class="cstat-no" title="statement not covered" >  label,</span>
<span class="cstat-no" title="statement not covered" >  position = 'right'</span>
<span class="cstat-no" title="statement not covered" >}) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  if (!show) return null;</span>
<span class="cstat-no" title="statement not covered" >  return &lt;div className={`absolute ${position === 'right' ? 'left-full ml-2' : 'right-full mr-2'} top-1/2 -translate-y-1/2 px-2 py-1 rounded bg-black/80 text-white text-xs whitespace-nowrap z-50`} style={{</span>
<span class="cstat-no" title="statement not covered" >    pointerEvents: 'none'</span>
<span class="cstat-no" title="statement not covered" >  }}&gt;</span>
<span class="cstat-no" title="statement not covered" >      {label}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className={`absolute top-1/2 -translate-y-1/2 ${position === 'right' ? 'left-0 -translate-x-full' : 'right-0 translate-x-full'} border-4 ${position === 'right' ? 'border-r-black/80 border-transparent' : 'border-l-black/80 border-transparent'}`}&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;;</span>
<span class="cstat-no" title="statement not covered" >};</span>
<span class="cstat-no" title="statement not covered" >/**</span>
<span class="cstat-no" title="statement not covered" > * SideNavigation - A vertical navigation component</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * This component renders a navigation sidebar with icons and the application logo.</span>
<span class="cstat-no" title="statement not covered" > * It highlights the active route and provides hover effects.</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" >export const SideNavigation: React.FC&lt;SideNavigationProps&gt; = ({</span>
<span class="cstat-no" title="statement not covered" >  className = '',</span>
<span class="cstat-no" title="statement not covered" >  'data-id': dataId</span>
<span class="cstat-no" title="statement not covered" >}) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  // State to track which tooltip is currently visible</span>
<span class="cstat-no" title="statement not covered" >  const [activeTooltip, setActiveTooltip] = useState&lt;string | null&gt;(null);</span>
<span class="cstat-no" title="statement not covered" >  const { projectsEnabled } = useSettings();</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Default navigation items</span>
<span class="cstat-no" title="statement not covered" >  const navigationItems: NavigationItem[] = [{</span>
<span class="cstat-no" title="statement not covered" >    path: '/',</span>
<span class="cstat-no" title="statement not covered" >    icon: &lt;BookOpen className="h-5 w-5" /&gt;,</span>
<span class="cstat-no" title="statement not covered" >    label: 'Knowledge Base'</span>
<span class="cstat-no" title="statement not covered" >  }, {</span>
<span class="cstat-no" title="statement not covered" >    path: '/mcp',</span>
<span class="cstat-no" title="statement not covered" >    icon: &lt;svg fill="currentColor" fillRule="evenodd" height="20" width="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"&gt;&lt;path d="M15.688 2.343a2.588 2.588 0 00-3.61 0l-9.626 9.44a.863.863 0 01-1.203 0 .823.823 0 010-1.18l9.626-9.44a4.313 4.313 0 016.016 0 4.116 4.116 0 011.204 3.54 4.3 4.3 0 013.609 1.18l.05.05a4.115 4.115 0 010 5.9l-8.706 8.537a.274.274 0 000 .393l1.788 1.754a.823.823 0 010 1.18.863.863 0 01-1.203 0l-1.788-1.753a1.92 1.92 0 010-2.754l8.706-8.538a2.47 2.47 0 000-3.54l-.05-.049a2.588 2.588 0 00-3.607-.003l-7.172 7.034-.002.002-.098.097a.863.863 0 01-1.204 0 .823.823 0 010-1.18l7.273-7.133a2.47 2.47 0 00-.003-3.537z"&gt;&lt;/path&gt;&lt;path d="M14.485 4.703a.823.823 0 000-1.18.863.863 0 00-1.204 0l-7.119 6.982a4.115 4.115 0 000 5.9 4.314 4.314 0 006.016 0l7.12-6.982a.823.823 0 000-1.18.863.863 0 00-1.204 0l-7.119 6.982a2.588 2.588 0 01-3.61 0 2.47 2.47 0 010-3.54l7.12-6.982z"&gt;&lt;/path&gt;&lt;/svg&gt;,</span>
<span class="cstat-no" title="statement not covered" >    label: 'MCP Server'</span>
<span class="cstat-no" title="statement not covered" >  }, {</span>
<span class="cstat-no" title="statement not covered" >    path: '/settings',</span>
<span class="cstat-no" title="statement not covered" >    icon: &lt;Settings className="h-5 w-5" /&gt;,</span>
<span class="cstat-no" title="statement not covered" >    label: 'Settings'</span>
<span class="cstat-no" title="statement not covered" >  }];</span>
<span class="cstat-no" title="statement not covered" >  // Logo configuration</span>
<span class="cstat-no" title="statement not covered" >  const logoSrc = "/logo-neon.png";</span>
<span class="cstat-no" title="statement not covered" >  const logoAlt = 'Knowledge Base Logo';</span>
<span class="cstat-no" title="statement not covered" >  // Get current location to determine active route</span>
<span class="cstat-no" title="statement not covered" >  const location = useLocation();</span>
<span class="cstat-no" title="statement not covered" >  const isProjectsActive = location.pathname === '/projects' &amp;&amp; projectsEnabled;</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  const logoClassName = `</span>
<span class="cstat-no" title="statement not covered" >    logo-container p-2 relative rounded-lg transition-all duration-300</span>
<span class="cstat-no" title="statement not covered" >    ${isProjectsActive ? 'bg-gradient-to-b from-white/20 to-white/5 dark:from-white/10 dark:to-black/20 shadow-[0_5px_15px_-5px_rgba(59,130,246,0.3)] dark:shadow-[0_5px_15px_-5px_rgba(59,130,246,0.5)] transform scale-110' : ''}</span>
<span class="cstat-no" title="statement not covered" >    ${projectsEnabled ? 'hover:bg-white/10 dark:hover:bg-white/5 cursor-pointer' : 'opacity-50 cursor-not-allowed'}</span>
<span class="cstat-no" title="statement not covered" >  `;</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  return &lt;div data-id={dataId} className={`flex flex-col items-center gap-6 py-6 px-3 rounded-xl backdrop-blur-md bg-gradient-to-b from-white/80 to-white/60 dark:from-white/10 dark:to-black/30 border border-gray-200 dark:border-zinc-800/50 shadow-[0_10px_30px_-15px_rgba(0,0,0,0.1)] dark:shadow-[0_10px_30px_-15px_rgba(0,0,0,0.7)] ${className}`}&gt;</span>
<span class="cstat-no" title="statement not covered" >      {/* Logo - Conditionally clickable based on Projects enabled */}</span>
<span class="cstat-no" title="statement not covered" >      {projectsEnabled ? (</span>
<span class="cstat-no" title="statement not covered" >        &lt;Link </span>
<span class="cstat-no" title="statement not covered" >          to="/projects"</span>
<span class="cstat-no" title="statement not covered" >          className={logoClassName}</span>
<span class="cstat-no" title="statement not covered" >          onMouseEnter={() =&gt; setActiveTooltip('logo')} </span>
<span class="cstat-no" title="statement not covered" >          onMouseLeave={() =&gt; setActiveTooltip(null)}</span>
<span class="cstat-no" title="statement not covered" >        &gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;img src={logoSrc} alt={logoAlt} className={`w-8 h-8 transition-all duration-300 ${isProjectsActive ? 'filter drop-shadow-[0_0_8px_rgba(59,130,246,0.7)]' : ''}`} /&gt;</span>
<span class="cstat-no" title="statement not covered" >          {/* Active state decorations */}</span>
<span class="cstat-no" title="statement not covered" >          {isProjectsActive &amp;&amp; &lt;&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="absolute inset-0 rounded-lg border border-blue-300 dark:border-blue-500/30"&gt;&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="absolute bottom-0 left-[15%] right-[15%] w-[70%] mx-auto h-[2px] bg-blue-500 shadow-[0_0_10px_2px_rgba(59,130,246,0.4)] dark:shadow-[0_0_20px_5px_rgba(59,130,246,0.7)]"&gt;&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/&gt;}</span>
<span class="cstat-no" title="statement not covered" >          &lt;NavTooltip show={activeTooltip === 'logo'} label="Project Management" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/Link&gt;</span>
<span class="cstat-no" title="statement not covered" >      ) : (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div </span>
<span class="cstat-no" title="statement not covered" >          className={logoClassName}</span>
<span class="cstat-no" title="statement not covered" >          onMouseEnter={() =&gt; setActiveTooltip('logo')} </span>
<span class="cstat-no" title="statement not covered" >          onMouseLeave={() =&gt; setActiveTooltip(null)}</span>
<span class="cstat-no" title="statement not covered" >        &gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;img src={logoSrc} alt={logoAlt} className="w-8 h-8 transition-all duration-300" /&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;NavTooltip show={activeTooltip === 'logo'} label="Projects Disabled" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      )}</span>
<span class="cstat-no" title="statement not covered" >      {/* Navigation links */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;nav className="flex flex-col gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >        {navigationItems.map(item =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const isActive = location.pathname === item.path;</span>
<span class="cstat-no" title="statement not covered" >        return &lt;Link key={item.path} to={item.path} className={`</span>
<span class="cstat-no" title="statement not covered" >                relative p-3 rounded-lg flex items-center justify-center</span>
<span class="cstat-no" title="statement not covered" >                transition-all duration-300</span>
<span class="cstat-no" title="statement not covered" >                ${isActive ? 'bg-gradient-to-b from-white/20 to-white/5 dark:from-white/10 dark:to-black/20 text-blue-600 dark:text-blue-400 shadow-[0_5px_15px_-5px_rgba(59,130,246,0.3)] dark:shadow-[0_5px_15px_-5px_rgba(59,130,246,0.5)]' : 'text-gray-500 dark:text-zinc-500 hover:text-blue-600 dark:hover:text-blue-400'}</span>
<span class="cstat-no" title="statement not covered" >              `} onMouseEnter={() =&gt; setActiveTooltip(item.path)} onMouseLeave={() =&gt; setActiveTooltip(null)} aria-label={item.label}&gt;</span>
<span class="cstat-no" title="statement not covered" >              {/* Active state decorations - Modified to place neon line below button with adjusted width */}</span>
<span class="cstat-no" title="statement not covered" >              {isActive &amp;&amp; &lt;&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="absolute inset-0 rounded-lg border border-blue-300 dark:border-blue-500/30"&gt;&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {/* Neon line positioned below the button with reduced width to respect curved edges */}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="absolute bottom-0 left-[15%] right-[15%] w-[70%] mx-auto h-[2px] bg-blue-500 shadow-[0_0_10px_2px_rgba(59,130,246,0.4)] dark:shadow-[0_0_20px_5px_rgba(59,130,246,0.7)]"&gt;&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/&gt;}</span>
<span class="cstat-no" title="statement not covered" >              {item.icon}</span>
<span class="cstat-no" title="statement not covered" >              {/* Custom tooltip */}</span>
<span class="cstat-no" title="statement not covered" >              &lt;NavTooltip show={activeTooltip === item.path} label={item.label} /&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/Link&gt;;</span>
<span class="cstat-no" title="statement not covered" >      })}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/nav&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;;</span>
<span class="cstat-no" title="statement not covered" >};</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-28T08:39:05.993Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    