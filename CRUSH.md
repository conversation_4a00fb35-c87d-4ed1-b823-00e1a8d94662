# CRUSH.md - Archon Development Commands & Guidelines

## Build/Test Commands
- `make test-fe` - Run frontend tests (Vitest)
- `make test-be` - Run backend tests (uv run pytest)
- `npm run test:coverage` - Frontend coverage with custom reporting
- `uv run pytest tests/test_file.py::test_name` - Run single backend test
- `make lint` - Run all linters
- `make lint-be` - Run ruff on Python backend
- `make dev` - Start backend in Docker, frontend locally (recommended)

## Code Style Guidelines
**Python**: Ruff linting (120 chars, double quotes, spaces), mypy with `ignore_missing_imports=true`
**TypeScript**: ESLint allows `any` types, unused `_` prefix ignored, `@` alias for `src/`
**Error Handling**: Fail fast for critical errors, continue with logging for batch operations
**Data Quality**: Never store corrupted data (zero embeddings, null foreign keys) - skip failed items
**Testing**: Mock all database calls in tests, test files colocated with source files
**Dependencies**: Backend uses uv, not pip