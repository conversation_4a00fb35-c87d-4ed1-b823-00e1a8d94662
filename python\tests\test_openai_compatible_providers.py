"""
Comprehensive tests for OpenAI-compatible provider support.

Tests the unified OpenAI-compatible API architecture with various providers.
"""

import asyncio
import os
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from openai import AsyncOpenAI

from src.server.services.credential_service import credential_service
from src.server.services.llm_provider_service import (
    get_chat_model,
    get_embedding_model,
    get_llm_client,
    invalidate_cache,
    validate_endpoint,
)


class TestProviderCompatibility:
    """Test OpenAI-compatible provider configurations."""

    @pytest.fixture(autouse=True)
    async def setup(self):
        """Setup test environment."""
        # Clear cache before each test
        invalidate_cache()
        credential_service.invalidate_cache()
        yield
        # Cleanup after test
        invalidate_cache()
        credential_service.invalidate_cache()

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "provider_config",
        [
            {
                "name": "OpenAI",
                "chat_model": "gpt-4o-mini",
                "chat_base_url": "https://api.openai.com/v1",
                "chat_api_key": "test-openai-key",
                "embedding_model": "text-embedding-3-small",
                "embedding_base_url": "https://api.openai.com/v1",
                "embedding_api_key": "test-openai-key",
            },
            {
                "name": "Azure OpenAI",
                "chat_model": "gpt-4",
                "chat_base_url": "https://test.openai.azure.com/openai/deployments/gpt4",
                "chat_api_key": "test-azure-key",
                "embedding_model": "text-embedding-ada-002",
                "embedding_base_url": "https://test.openai.azure.com/openai/deployments/embeddings",
                "embedding_api_key": "test-azure-key",
            },
            {
                "name": "Ollama",
                "chat_model": "llama3.2",
                "chat_base_url": "http://localhost:11434/v1",
                "chat_api_key": "ollama",
                "embedding_model": "nomic-embed-text",
                "embedding_base_url": "http://localhost:11434/v1",
                "embedding_api_key": "ollama",
            },
            {
                "name": "LM Studio",
                "chat_model": "local-model",
                "chat_base_url": "http://localhost:1234/v1",
                "chat_api_key": "lm-studio",
                "embedding_model": "local-embedding",
                "embedding_base_url": "http://localhost:1234/v1",
                "embedding_api_key": "lm-studio",
            },
            {
                "name": "Together AI",
                "chat_model": "meta-llama/Llama-3-70b-chat-hf",
                "chat_base_url": "https://api.together.xyz/v1",
                "chat_api_key": "test-together-key",
                "embedding_model": "togethercomputer/m2-bert-80M-8k-retrieval",
                "embedding_base_url": "https://api.together.xyz/v1",
                "embedding_api_key": "test-together-key",
            },
            {
                "name": "Groq",
                "chat_model": "llama3-70b-8192",
                "chat_base_url": "https://api.groq.com/openai/v1",
                "chat_api_key": "test-groq-key",
                "embedding_model": "llama3-70b-8192",
                "embedding_base_url": "https://api.groq.com/openai/v1",
                "embedding_api_key": "test-groq-key",
            },
        ],
    )
    async def test_provider_compatibility(self, provider_config):
        """Test that various providers can be configured correctly."""
        # Mock credential service responses
        with patch.object(credential_service, "get_chat_config") as mock_chat_config:
            with patch.object(credential_service, "get_embedding_config") as mock_embed_config:
                # Setup mocks
                mock_chat_config.return_value = {
                    "model": provider_config["chat_model"],
                    "api_key": provider_config["chat_api_key"],
                    "base_url": provider_config["chat_base_url"],
                }
                mock_embed_config.return_value = {
                    "model": provider_config["embedding_model"],
                    "api_key": provider_config["embedding_api_key"],
                    "base_url": provider_config["embedding_base_url"],
                }

                # Test chat client creation
                async with get_llm_client(use_embedding_config=False) as client:
                    assert isinstance(client, AsyncOpenAI)
                    assert client.base_url == provider_config["chat_base_url"]
                    assert client.api_key == provider_config["chat_api_key"]

                # Test embedding client creation
                async with get_llm_client(use_embedding_config=True) as client:
                    assert isinstance(client, AsyncOpenAI)
                    assert client.base_url == provider_config["embedding_base_url"]
                    assert client.api_key == provider_config["embedding_api_key"]

                # Test model retrieval
                chat_model = await get_chat_model()
                assert chat_model == provider_config["chat_model"]

                embedding_model = await get_embedding_model()
                assert embedding_model == provider_config["embedding_model"]

    @pytest.mark.asyncio
    async def test_endpoint_validation(self):
        """Test endpoint validation functionality."""
        # Mock successful validation
        with patch("httpx.AsyncClient.get") as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"data": [{"id": "model-1"}]}
            mock_get.return_value = mock_response

            is_valid, message = await validate_endpoint(
                "https://api.openai.com/v1", "test-key"
            )
            assert is_valid is True
            assert "successfully" in message.lower()

        # Mock invalid API key
        with patch("httpx.AsyncClient.get") as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 401
            mock_get.return_value = mock_response

            is_valid, message = await validate_endpoint(
                "https://api.openai.com/v1", "invalid-key"
            )
            assert is_valid is False
            assert "invalid api key" in message.lower()

        # Mock endpoint not found
        with patch("httpx.AsyncClient.get") as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 404
            mock_get.return_value = mock_response

            is_valid, message = await validate_endpoint(
                "https://invalid.example.com/v1", "test-key"
            )
            assert is_valid is False
            assert "not found" in message.lower()

    @pytest.mark.asyncio
    async def test_cache_invalidation(self):
        """Test that cache invalidation works correctly."""
        with patch.object(credential_service, "get_chat_config") as mock_chat_config:
            # First call - should fetch from service
            mock_chat_config.return_value = {
                "model": "gpt-4o-mini",
                "api_key": "key1",
                "base_url": "https://api.openai.com/v1",
            }
            
            model1 = await get_chat_model()
            assert model1 == "gpt-4o-mini"
            assert mock_chat_config.call_count == 1

            # Second call - should use cache
            model2 = await get_chat_model()
            assert model2 == "gpt-4o-mini"
            assert mock_chat_config.call_count == 1  # Still 1, used cache

            # Invalidate cache
            invalidate_cache()

            # Third call - should fetch again
            mock_chat_config.return_value = {
                "model": "gpt-4",
                "api_key": "key2",
                "base_url": "https://api.openai.com/v1",
            }
            
            model3 = await get_chat_model()
            assert model3 == "gpt-4"
            assert mock_chat_config.call_count == 2  # Fetched again

    @pytest.mark.asyncio
    async def test_migration_scenarios(self):
        """Test data migration from old to new structure."""
        # Test OpenAI migration
        old_config = {
            "LLM_PROVIDER": "openai",
            "MODEL_CHOICE": "gpt-3.5-turbo",
            "EMBEDDING_MODEL": "text-embedding-ada-002",
        }
        
        # Expected new config after migration
        expected_new = {
            "CHAT_MODEL": "gpt-3.5-turbo",
            "CHAT_API_KEY": "OPENAI_API_KEY",
            "CHAT_BASE_URL": "https://api.openai.com/v1",
            "EMBEDDING_MODEL": "text-embedding-ada-002",
            "EMBEDDING_API_KEY": "OPENAI_API_KEY",
            "EMBEDDING_BASE_URL": "https://api.openai.com/v1",
        }
        
        # Verify migration logic (this would be tested against actual migration code)
        assert expected_new["CHAT_MODEL"] == old_config["MODEL_CHOICE"]
        assert expected_new["EMBEDDING_MODEL"] == old_config["EMBEDDING_MODEL"]

        # Test Ollama migration
        old_ollama = {
            "LLM_PROVIDER": "ollama",
            "MODEL_CHOICE": "llama2",
            "LLM_BASE_URL": "http://localhost:11434/v1",
            "EMBEDDING_MODEL": "nomic-embed-text",
        }
        
        expected_ollama = {
            "CHAT_MODEL": "llama2",
            "CHAT_API_KEY": "ollama",
            "CHAT_BASE_URL": "http://localhost:11434/v1",
            "EMBEDDING_MODEL": "nomic-embed-text",
            "EMBEDDING_API_KEY": "ollama",
            "EMBEDDING_BASE_URL": "http://localhost:11434/v1",
        }
        
        assert expected_ollama["CHAT_BASE_URL"] == old_ollama["LLM_BASE_URL"]
        assert expected_ollama["CHAT_API_KEY"] == "ollama"  # Dummy key for Ollama

    @pytest.mark.asyncio
    async def test_independent_configurations(self):
        """Test that chat and embedding can use different configurations."""
        with patch.object(credential_service, "get_chat_config") as mock_chat_config:
            with patch.object(credential_service, "get_embedding_config") as mock_embed_config:
                # Configure different providers for chat and embedding
                mock_chat_config.return_value = {
                    "model": "gpt-4",
                    "api_key": "openai-key",
                    "base_url": "https://api.openai.com/v1",
                }
                mock_embed_config.return_value = {
                    "model": "nomic-embed-text",
                    "api_key": "ollama",
                    "base_url": "http://localhost:11434/v1",
                }

                # Verify chat uses OpenAI
                async with get_llm_client(use_embedding_config=False) as chat_client:
                    assert chat_client.base_url == "https://api.openai.com/v1"
                    assert chat_client.api_key == "openai-key"

                # Verify embedding uses Ollama
                async with get_llm_client(use_embedding_config=True) as embed_client:
                    assert embed_client.base_url == "http://localhost:11434/v1"
                    assert embed_client.api_key == "ollama"

                # Verify models
                assert await get_chat_model() == "gpt-4"
                assert await get_embedding_model() == "nomic-embed-text"

    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in client creation."""
        # Test missing API key
        with patch.object(credential_service, "get_chat_config") as mock_chat_config:
            mock_chat_config.return_value = {
                "model": "gpt-4",
                "api_key": None,  # Missing API key
                "base_url": "https://api.openai.com/v1",
            }

            with pytest.raises(ValueError, match="API key not configured"):
                async with get_llm_client(use_embedding_config=False):
                    pass

        # Test missing base URL
        with patch.object(credential_service, "get_chat_config") as mock_chat_config:
            mock_chat_config.return_value = {
                "model": "gpt-4",
                "api_key": "test-key",
                "base_url": None,  # Missing base URL
            }

            with pytest.raises(ValueError, match="Base URL not configured"):
                async with get_llm_client(use_embedding_config=False):
                    pass

    @pytest.mark.asyncio
    async def test_backward_compatibility(self):
        """Test backward compatibility with legacy code."""
        with patch.object(credential_service, "get_embedding_config") as mock_embed_config:
            mock_embed_config.return_value = {
                "model": "text-embedding-3-small",
                "api_key": "test-key",
                "base_url": "https://api.openai.com/v1",
            }

            # Test legacy get_embedding_model with provider parameter
            model = await get_embedding_model(provider="openai")  # Legacy call
            assert model == "text-embedding-3-small"

            # Verify warning is logged for deprecated parameter
            # (In actual implementation, we'd check logs)

    @pytest.mark.asyncio
    async def test_custom_provider(self):
        """Test configuration with a custom OpenAI-compatible provider."""
        custom_config = {
            "model": "custom-model-v1",
            "api_key": "custom-api-key",
            "base_url": "https://custom.ai-provider.com/v1",
        }

        with patch.object(credential_service, "get_chat_config") as mock_chat_config:
            mock_chat_config.return_value = custom_config

            async with get_llm_client(use_embedding_config=False) as client:
                assert isinstance(client, AsyncOpenAI)
                assert client.base_url == custom_config["base_url"]
                assert client.api_key == custom_config["api_key"]

            model = await get_chat_model()
            assert model == custom_config["model"]


class TestMigration:
    """Test migration from old to new configuration structure."""

    @pytest.mark.asyncio
    async def test_migration_preserves_data(self):
        """Test that migration preserves existing configuration data."""
        # This would test the actual SQL migration script
        # For now, we verify the migration logic conceptually
        
        migration_mappings = [
            # (old_provider, old_model, expected_chat_model, expected_chat_url)
            ("openai", "gpt-3.5-turbo", "gpt-3.5-turbo", "https://api.openai.com/v1"),
            ("ollama", "llama3", "llama3", "http://localhost:11434/v1"),
            ("google", "gemini-1.5-flash", "gemini-1.5-flash", "https://generativelanguage.googleapis.com/v1beta/openai/"),
        ]

        for old_provider, old_model, expected_model, expected_url in migration_mappings:
            # Verify migration logic
            assert expected_model == old_model  # Model name preserved
            assert expected_url is not None  # URL is set


class TestIntegration:
    """Integration tests for the complete flow."""

    @pytest.mark.asyncio
    async def test_end_to_end_openai(self):
        """Test complete flow with OpenAI provider."""
        # Mock environment setup
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}):
            with patch.object(credential_service, "get_chat_config") as mock_chat:
                with patch.object(credential_service, "get_embedding_config") as mock_embed:
                    mock_chat.return_value = {
                        "model": "gpt-4o-mini",
                        "api_key": "test-key",
                        "base_url": "https://api.openai.com/v1",
                    }
                    mock_embed.return_value = {
                        "model": "text-embedding-3-small",
                        "api_key": "test-key",
                        "base_url": "https://api.openai.com/v1",
                    }

                    # Create chat client
                    async with get_llm_client(use_embedding_config=False) as chat_client:
                        assert chat_client is not None
                        # Would test actual API call here in integration environment

                    # Create embedding client  
                    async with get_llm_client(use_embedding_config=True) as embed_client:
                        assert embed_client is not None
                        # Would test actual embedding creation here

    @pytest.mark.asyncio
    async def test_provider_switching(self):
        """Test switching between providers."""
        providers = [
            {
                "model": "gpt-4",
                "api_key": "openai-key",
                "base_url": "https://api.openai.com/v1",
            },
            {
                "model": "llama3",
                "api_key": "ollama",
                "base_url": "http://localhost:11434/v1",
            },
        ]

        for provider_config in providers:
            invalidate_cache()  # Clear cache between switches
            
            with patch.object(credential_service, "get_chat_config") as mock_config:
                mock_config.return_value = provider_config

                async with get_llm_client(use_embedding_config=False) as client:
                    assert client.base_url == provider_config["base_url"]
                    assert client.api_key == provider_config["api_key"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])