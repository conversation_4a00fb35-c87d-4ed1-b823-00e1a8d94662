
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/animations/DisconnectScreenAnimations.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/components/animations</a> DisconnectScreenAnimations.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/175</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/175</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >import React, { useEffect, useRef } from 'react';</span></span></span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >/**</span>
<span class="cstat-no" title="statement not covered" > * Disconnect Screen</span>
<span class="cstat-no" title="statement not covered" > * Frosted glass medallion with aurora borealis light show behind it</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" >export const DisconnectScreen: React.FC = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const canvasRef = useRef&lt;HTMLCanvasElement&gt;(null);</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const canvas = canvasRef.current;</span>
<span class="cstat-no" title="statement not covered" >    if (!canvas) return;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const ctx = canvas.getContext('2d');</span>
<span class="cstat-no" title="statement not covered" >    if (!ctx) return;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    canvas.width = window.innerWidth;</span>
<span class="cstat-no" title="statement not covered" >    canvas.height = window.innerHeight;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    let time = 0;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const drawAurora = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      // Create dark background with vignette</span>
<span class="cstat-no" title="statement not covered" >      const gradient = ctx.createRadialGradient(</span>
<span class="cstat-no" title="statement not covered" >        canvas.width / 2, canvas.height / 2, 0,</span>
<span class="cstat-no" title="statement not covered" >        canvas.width / 2, canvas.height / 2, canvas.width / 1.5</span>
<span class="cstat-no" title="statement not covered" >      );</span>
<span class="cstat-no" title="statement not covered" >      gradient.addColorStop(0, 'rgba(0, 0, 0, 0.3)');</span>
<span class="cstat-no" title="statement not covered" >      gradient.addColorStop(1, 'rgba(0, 0, 0, 0.95)');</span>
<span class="cstat-no" title="statement not covered" >      ctx.fillStyle = gradient;</span>
<span class="cstat-no" title="statement not covered" >      ctx.fillRect(0, 0, canvas.width, canvas.height);</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >      // Draw aurora waves with varying opacity</span>
<span class="cstat-no" title="statement not covered" >      const colors = [</span>
<span class="cstat-no" title="statement not covered" >        { r: 34, g: 211, b: 238, a: 0.4 },  // Cyan</span>
<span class="cstat-no" title="statement not covered" >        { r: 168, g: 85, b: 247, a: 0.4 },  // Purple</span>
<span class="cstat-no" title="statement not covered" >        { r: 236, g: 72, b: 153, a: 0.4 },  // Pink</span>
<span class="cstat-no" title="statement not covered" >        { r: 59, g: 130, b: 246, a: 0.4 },  // Blue</span>
<span class="cstat-no" title="statement not covered" >        { r: 16, g: 185, b: 129, a: 0.4 },  // Green</span>
<span class="cstat-no" title="statement not covered" >      ];</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >      colors.forEach((color, index) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        ctx.beginPath();</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        const waveHeight = 250;</span>
<span class="cstat-no" title="statement not covered" >        const waveOffset = index * 60;</span>
<span class="cstat-no" title="statement not covered" >        const speed = 0.001 + index * 0.0002;</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        // Animate opacity for ethereal effect</span>
<span class="cstat-no" title="statement not covered" >        const opacityWave = Math.sin(time * 0.0005 + index) * 0.2 + 0.3;</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        for (let x = 0; x &lt;= canvas.width; x += 5) {</span>
<span class="cstat-no" title="statement not covered" >          const y = canvas.height / 2 + </span>
<span class="cstat-no" title="statement not covered" >            Math.sin(x * 0.003 + time * speed) * waveHeight +</span>
<span class="cstat-no" title="statement not covered" >            Math.sin(x * 0.005 + time * speed * 1.5) * (waveHeight / 2) +</span>
<span class="cstat-no" title="statement not covered" >            Math.sin(x * 0.002 + time * speed * 0.5) * (waveHeight / 3) +</span>
<span class="cstat-no" title="statement not covered" >            waveOffset - 100;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          if (x === 0) {</span>
<span class="cstat-no" title="statement not covered" >            ctx.moveTo(x, y);</span>
<span class="cstat-no" title="statement not covered" >          } else {</span>
<span class="cstat-no" title="statement not covered" >            ctx.lineTo(x, y);</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        // Create gradient for each wave with animated opacity</span>
<span class="cstat-no" title="statement not covered" >        const waveGradient = ctx.createLinearGradient(0, canvas.height / 2 - 300, 0, canvas.height / 2 + 300);</span>
<span class="cstat-no" title="statement not covered" >        waveGradient.addColorStop(0, `rgba(${color.r}, ${color.g}, ${color.b}, 0)`);</span>
<span class="cstat-no" title="statement not covered" >        waveGradient.addColorStop(0.5, `rgba(${color.r}, ${color.g}, ${color.b}, ${opacityWave})`);</span>
<span class="cstat-no" title="statement not covered" >        waveGradient.addColorStop(1, `rgba(${color.r}, ${color.g}, ${color.b}, 0)`);</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        ctx.strokeStyle = waveGradient;</span>
<span class="cstat-no" title="statement not covered" >        ctx.lineWidth = 4;</span>
<span class="cstat-no" title="statement not covered" >        ctx.stroke();</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        // Add enhanced glow effect</span>
<span class="cstat-no" title="statement not covered" >        ctx.shadowBlur = 40;</span>
<span class="cstat-no" title="statement not covered" >        ctx.shadowColor = `rgba(${color.r}, ${color.g}, ${color.b}, 0.6)`;</span>
<span class="cstat-no" title="statement not covered" >        ctx.stroke();</span>
<span class="cstat-no" title="statement not covered" >        ctx.shadowBlur = 0;</span>
<span class="cstat-no" title="statement not covered" >      });</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >      time += 16;</span>
<span class="cstat-no" title="statement not covered" >      requestAnimationFrame(drawAurora);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    drawAurora();</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const handleResize = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      canvas.width = window.innerWidth;</span>
<span class="cstat-no" title="statement not covered" >      canvas.height = window.innerHeight;</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    window.addEventListener('resize', handleResize);</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    return () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      window.removeEventListener('resize', handleResize);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" >  }, []);</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="relative w-full h-full bg-black overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;canvas ref={canvasRef} className="absolute inset-0" /&gt;</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      {/* Glass medallion with frosted effect - made bigger */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="absolute inset-0 flex items-center justify-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="relative"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {/* Glowing orb effect */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div </span>
<span class="cstat-no" title="statement not covered" >            className="absolute inset-0 w-96 h-96 rounded-full"</span>
<span class="cstat-no" title="statement not covered" >            style={{</span>
<span class="cstat-no" title="statement not covered" >              background: 'radial-gradient(circle, rgba(34, 211, 238, 0.3) 0%, rgba(168, 85, 247, 0.2) 40%, transparent 70%)',</span>
<span class="cstat-no" title="statement not covered" >              filter: 'blur(40px)',</span>
<span class="cstat-no" title="statement not covered" >              animation: 'glow 4s ease-in-out infinite',</span>
<span class="cstat-no" title="statement not covered" >            }}</span>
<span class="cstat-no" title="statement not covered" >          /&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* Frosted glass background */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div </span>
<span class="cstat-no" title="statement not covered" >            className="absolute inset-0 w-96 h-96 rounded-full"</span>
<span class="cstat-no" title="statement not covered" >            style={{</span>
<span class="cstat-no" title="statement not covered" >              background: 'radial-gradient(circle, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.03) 50%, transparent 100%)',</span>
<span class="cstat-no" title="statement not covered" >              backdropFilter: 'blur(20px)',</span>
<span class="cstat-no" title="statement not covered" >              border: '3px solid rgba(255,255,255,0.25)',</span>
<span class="cstat-no" title="statement not covered" >              boxShadow: `</span>
<span class="cstat-no" title="statement not covered" >                inset 0 0 40px rgba(255,255,255,0.1), </span>
<span class="cstat-no" title="statement not covered" >                0 0 80px rgba(34, 211, 238, 0.5),</span>
<span class="cstat-no" title="statement not covered" >                0 0 120px rgba(168, 85, 247, 0.4),</span>
<span class="cstat-no" title="statement not covered" >                0 0 160px rgba(34, 211, 238, 0.3),</span>
<span class="cstat-no" title="statement not covered" >                0 0 200px rgba(168, 85, 247, 0.2)</span>
<span class="cstat-no" title="statement not covered" >              `,</span>
<span class="cstat-no" title="statement not covered" >            }}</span>
<span class="cstat-no" title="statement not covered" >          /&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* Embossed logo - made bigger */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="relative w-96 h-96 flex items-center justify-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;img </span>
<span class="cstat-no" title="statement not covered" >              src="/logo-neon.png" </span>
<span class="cstat-no" title="statement not covered" >              alt="Archon" </span>
<span class="cstat-no" title="statement not covered" >              className="w-64 h-64 z-10"</span>
<span class="cstat-no" title="statement not covered" >              style={{</span>
<span class="cstat-no" title="statement not covered" >                filter: 'drop-shadow(0 3px 6px rgba(0,0,0,0.4)) drop-shadow(0 -2px 4px rgba(255,255,255,0.3))',</span>
<span class="cstat-no" title="statement not covered" >                opacity: 0.9,</span>
<span class="cstat-no" title="statement not covered" >                mixBlendMode: 'screen',</span>
<span class="cstat-no" title="statement not covered" >              }}</span>
<span class="cstat-no" title="statement not covered" >            /&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* Disconnected Text - Glass style with red glow */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="absolute -bottom-20 left-1/2 transform -translate-x-1/2"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div </span>
<span class="cstat-no" title="statement not covered" >              className="px-8 py-4 rounded-full"</span>
<span class="cstat-no" title="statement not covered" >              style={{</span>
<span class="cstat-no" title="statement not covered" >                background: 'rgba(255, 255, 255, 0.05)',</span>
<span class="cstat-no" title="statement not covered" >                backdropFilter: 'blur(10px)',</span>
<span class="cstat-no" title="statement not covered" >                border: '1px solid rgba(255, 255, 255, 0.1)',</span>
<span class="cstat-no" title="statement not covered" >                boxShadow: '0 0 30px rgba(239, 68, 68, 0.5), inset 0 0 20px rgba(239, 68, 68, 0.2)',</span>
<span class="cstat-no" title="statement not covered" >              }}</span>
<span class="cstat-no" title="statement not covered" >            &gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span </span>
<span class="cstat-no" title="statement not covered" >                className="text-2xl font-medium tracking-wider"</span>
<span class="cstat-no" title="statement not covered" >                style={{</span>
<span class="cstat-no" title="statement not covered" >                  color: 'rgba(239, 68, 68, 0.9)',</span>
<span class="cstat-no" title="statement not covered" >                  textShadow: '0 0 20px rgba(239, 68, 68, 0.8), 0 0 40px rgba(239, 68, 68, 0.6)',</span>
<span class="cstat-no" title="statement not covered" >                }}</span>
<span class="cstat-no" title="statement not covered" >              &gt;</span>
<span class="cstat-no" title="statement not covered" >                DISCONNECTED</span>
<span class="cstat-no" title="statement not covered" >              &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  );</span>
<span class="cstat-no" title="statement not covered" >};</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-28T08:39:05.494Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    