"""
LLM Provider Service

Provides a unified interface for creating OpenAI-compatible clients.
All providers must support the OpenAI API standard.
"""

import time
from contextlib import asynccontextmanager
from typing import Any

import openai

from ..config.logfire_config import get_logger
from .credential_service import credential_service

logger = get_logger(__name__)

# Settings cache with TTL
_settings_cache: dict[str, tuple[Any, float]] = {}
_CACHE_TTL_SECONDS = 300  # 5 minutes


def _get_cached_settings(key: str) -> Any | None:
    """Get cached settings if not expired."""
    if key in _settings_cache:
        value, timestamp = _settings_cache[key]
        if time.time() - timestamp < _CACHE_TTL_SECONDS:
            return value
        else:
            # Expired, remove from cache
            del _settings_cache[key]
    return None


def _set_cached_settings(key: str, value: Any) -> None:
    """Cache settings with current timestamp."""
    _settings_cache[key] = (value, time.time())


def invalidate_cache() -> None:
    """Invalidate all cached settings."""
    _settings_cache.clear()
    logger.info("LLM provider cache invalidated")


@asynccontextmanager
async def get_llm_client(use_embedding_config: bool = False):
    """
    Create an async OpenAI-compatible client based on configuration.

    This context manager creates clients for any OpenAI API-compatible provider.

    Args:
        use_embedding_config: If True, use embedding configuration; otherwise use chat configuration

    Yields:
        openai.AsyncOpenAI: An OpenAI-compatible client configured for the selected endpoint
    """
    client = None

    try:
        # Determine which configuration to use
        cache_key = "embedding_config" if use_embedding_config else "chat_config"
        
        # Check cache first
        config = _get_cached_settings(cache_key)
        if config is None:
            # Get configuration from credential service
            if use_embedding_config:
                config = await credential_service.get_embedding_config()
            else:
                config = await credential_service.get_chat_config()
            
            _set_cached_settings(cache_key, config)
            logger.debug(f"Fetched and cached {cache_key}")
        else:
            logger.debug(f"Using cached {cache_key}")

        # Extract configuration
        api_key = config.get("api_key")
        base_url = config.get("base_url", "https://api.openai.com/v1")
        model = config.get("model")

        logger.info(f"Creating OpenAI-compatible client for: {base_url}")
        logger.debug(f"Using model: {model}")

        # Validate configuration
        if not api_key:
            raise ValueError("API key not configured")
        if not base_url:
            raise ValueError("Base URL not configured")

        # Create OpenAI-compatible client
        # All providers must support the OpenAI API standard
        client = openai.AsyncOpenAI(
            api_key=api_key,
            base_url=base_url,
        )
        
        logger.info(f"OpenAI-compatible client created successfully for {base_url}")

        yield client

    except Exception as e:
        logger.error(f"Error creating OpenAI-compatible client: {e}")
        raise
    finally:
        # Cleanup if needed
        pass


async def get_chat_model() -> str:
    """
    Get the configured chat model.

    Returns:
        str: The chat model to use
    """
    try:
        # Check cache first
        cache_key = "chat_config"
        config = _get_cached_settings(cache_key)
        if config is None:
            config = await credential_service.get_chat_config()
            _set_cached_settings(cache_key, config)
        
        model = config.get("model", "gpt-4o-mini")
        logger.debug(f"Using chat model: {model}")
        return model

    except Exception as e:
        logger.error(f"Error getting chat model: {e}")
        # Fallback to default
        return "gpt-4o-mini"


async def get_embedding_model() -> str:
    """
    Get the configured embedding model.

    Returns:
        str: The embedding model to use
    """
    try:
        # Check cache first
        cache_key = "embedding_config"
        config = _get_cached_settings(cache_key)
        if config is None:
            config = await credential_service.get_embedding_config()
            _set_cached_settings(cache_key, config)
        
        model = config.get("model", "text-embedding-3-small")
        logger.debug(f"Using embedding model: {model}")
        return model

    except Exception as e:
        logger.error(f"Error getting embedding model: {e}")
        # Fallback to default
        return "text-embedding-3-small"


async def validate_endpoint(base_url: str, api_key: str) -> tuple[bool, str]:
    """
    Validate an OpenAI-compatible endpoint.
    
    Args:
        base_url: The base URL to validate
        api_key: The API key to use
        
    Returns:
        Tuple of (is_valid, message)
    """
    return await credential_service.validate_openai_endpoint(base_url, api_key)


# Backward compatibility: Map old function signatures
async def get_embedding_model(provider: str | None = None) -> str:
    """
    Get the configured embedding model.
    Legacy signature for backward compatibility.
    
    Args:
        provider: Ignored - configuration now comes from credential service
        
    Returns:
        str: The embedding model to use
    """
    if provider:
        logger.warning(f"Provider parameter '{provider}' is deprecated and will be ignored")
    
    # Check cache first
    cache_key = "embedding_config"
    config = _get_cached_settings(cache_key)
    if config is None:
        config = await credential_service.get_embedding_config()
        _set_cached_settings(cache_key, config)
    
    model = config.get("model", "text-embedding-3-small")
    logger.debug(f"Using embedding model: {model}")
    return model