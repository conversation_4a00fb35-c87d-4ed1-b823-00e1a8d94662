# Plano de Migração para Python 3.12

## Resumo Executivo

A aplicação Archon Turbo atualmente utiliza **Python 3.11** nos containers Docker, enquanto o `pyproject.toml` já está configurado para **Python 3.12**. Esta migração visa alinhar completamente a aplicação para utilizar Python 3.12 em todos os ambientes, garantindo consistência e aproveitando as melhorias de performance e funcionalidades da versão mais recente.

## Análise da Situação Atual

### Estado Atual das Versões Python

| Componente | Versão Atual | Versão Alvo |
|------------|--------------|-------------|
| `pyproject.toml` | `>=3.12` ✅ | `>=3.12` |
| `pyrightconfig.json` | `3.12` ✅ | `3.12` |
| `uv.lock` | `>=3.12` ✅ | `>=3.12` |
| CI/CD (GitHub Actions) | `3.12` ✅ | `3.12` |
| Dockerfile.server | `3.11` ❌ | `3.12` |
| Dockerfile.agents | `3.11` ❌ | `3.12` |
| Dockerfile.mcp | `3.11` ❌ | `3.12` |
| Ruff target-version | `py312` ✅ | `py312` |
| MyPy python_version | `3.12` ✅ | `3.12` |

### Dependências Principais

**Dependências Críticas Analisadas:**
- `crawl4ai==0.6.2` - Compatível com Python 3.12
- `pydantic-ai>=0.0.13` - Compatível com Python 3.12
- `mcp==1.7.1` - Compatível com Python 3.12
- `fastapi>=0.104.0` - Compatível com Python 3.12
- `pydantic>=2.0.0` - Compatível com Python 3.12
- `supabase==2.15.1` - Compatível com Python 3.12

## Riscos Identificados

### Riscos Baixos ✅
1. **Configuração já alinhada**: `pyproject.toml`, `uv.lock`, e ferramentas de desenvolvimento já estão configuradas para Python 3.12
2. **Dependências compatíveis**: Todas as dependências principais suportam Python 3.12
3. **Código moderno**: O código utiliza funcionalidades compatíveis (async/await, type hints, dataclasses)

### Riscos Médios ⚠️
1. **Playwright**: Pode necessitar reinstalação dos browsers após mudança de versão Python
2. **Bibliotecas ML comentadas**: `sentence-transformers`, `torch`, `transformers` estão comentadas mas podem ser ativadas futuramente

### Riscos Altos ❌
- **Nenhum risco alto identificado**

## Plano de Implementação

### Fase 1: Preparação e Validação (Estimativa: 2 horas)

#### 1.1 Backup e Preparação
- [ ] Criar branch `migration/python-3.12`
- [ ] Documentar versões atuais de todas as dependências
- [ ] Fazer backup dos arquivos de lock atuais

#### 1.2 Análise de Compatibilidade Detalhada
- [ ] Executar testes locais com Python 3.12
- [ ] Verificar compatibilidade de todas as dependências
- [ ] Testar build local dos containers com Python 3.12

### Fase 2: Atualização dos Dockerfiles (Estimativa: 1 hora)

#### 2.1 Dockerfile.server
```dockerfile
# Mudança da linha 4 e 18
FROM python:3.12 AS builder
FROM python:3.12-slim
```

#### 2.2 Dockerfile.agents
```dockerfile
# Mudança da linha 2
FROM python:3.12-slim
```

#### 2.3 Dockerfile.mcp
```dockerfile
# Mudança da linha 2
FROM python:3.12-slim
```

#### 2.4 Atualização de Variáveis de Ambiente
```dockerfile
# Em Dockerfile.server, linha 61
ENV PYTHONPATH="/app:/root/.local/lib/python3.12/site-packages:$PYTHONPATH"
```

### Fase 3: Testes de Integração (Estimativa: 3 horas)

#### 3.1 Testes de Build
- [ ] Build individual de cada container
- [ ] Verificação de tamanho dos containers
- [ ] Teste de inicialização de cada serviço

#### 3.2 Testes de Funcionalidade
- [ ] Teste de conectividade entre serviços
- [ ] Teste de funcionalidades MCP
- [ ] Teste de crawling e processamento de documentos
- [ ] Teste de agentes PydanticAI

#### 3.3 Testes de Performance
- [ ] Benchmark de performance antes/depois
- [ ] Teste de uso de memória
- [ ] Teste de tempo de inicialização

### Fase 4: Testes Automatizados (Estimativa: 2 horas)

#### 4.1 Execução da Suite de Testes
```bash
# Testes unitários
uv run pytest tests/ -v

# Testes de integração
uv run pytest tests/ -m integration

# Testes com cobertura
uv run pytest tests/ --cov=src --cov-report=html
```

#### 4.2 Testes Específicos por Serviço
- [ ] Testes do servidor (FastAPI + Socket.IO)
- [ ] Testes do MCP server
- [ ] Testes dos agentes
- [ ] Testes de crawling

### Fase 5: Validação em Ambiente de Desenvolvimento (Estimativa: 2 horas)

#### 5.1 Docker Compose
- [ ] `docker-compose up --build`
- [ ] Verificação de health checks
- [ ] Teste de comunicação entre serviços

#### 5.2 Testes End-to-End
- [ ] Teste completo de criação de projeto
- [ ] Teste de upload e processamento de documentos
- [ ] Teste de funcionalidades de chat
- [ ] Teste de interface web

## Comandos de Execução

### Build e Teste Local
```bash
# 1. Fazer backup
cp python/uv.lock python/uv.lock.backup

# 2. Build dos containers
docker-compose build --no-cache

# 3. Executar testes
cd python
uv run pytest tests/ -v

# 4. Iniciar aplicação
docker-compose up

# 5. Verificar health checks
curl http://localhost:8181/health
curl http://localhost:8051/health  
curl http://localhost:8052/health
```

### Rollback (se necessário)
```bash
# Reverter para Python 3.11
git checkout HEAD~1 -- python/Dockerfile.*
docker-compose build --no-cache
```

## Critérios de Sucesso

### Critérios Obrigatórios ✅
1. **Todos os containers iniciam sem erro**
2. **Todos os health checks passam**
3. **Suite de testes completa com 100% de sucesso**
4. **Funcionalidades principais funcionam**:
   - Criação de projetos
   - Upload de documentos
   - Crawling de URLs
   - Chat com agentes
   - Interface web responsiva

### Critérios de Performance 📊
1. **Tempo de inicialização ≤ tempo atual + 10%**
2. **Uso de memória ≤ uso atual + 5%**
3. **Tempo de resposta das APIs ≤ tempo atual + 5%**

### Critérios de Qualidade 🔍
1. **Cobertura de testes ≥ 80%**
2. **Linting sem erros (ruff)**
3. **Type checking sem erros (mypy)**
4. **Documentação atualizada**

## Plano de Rollback

### Cenários de Rollback
1. **Falha nos testes**: Reverter Dockerfiles e reexecutar testes
2. **Problemas de performance**: Comparar métricas e decidir
3. **Incompatibilidade de dependências**: Investigar e corrigir ou reverter

### Procedimento de Rollback
```bash
# 1. Parar serviços
docker-compose down

# 2. Reverter mudanças
git checkout HEAD~1 -- python/Dockerfile.*

# 3. Rebuild com versão anterior
docker-compose build --no-cache

# 4. Reiniciar
docker-compose up

# 5. Verificar funcionamento
./scripts/health-check.sh
```

## Cronograma Estimado

| Fase | Duração | Responsável | Dependências |
|------|---------|-------------|--------------|
| Preparação | 2h | Dev Team | - |
| Dockerfiles | 1h | Dev Team | Fase 1 |
| Testes Integração | 3h | Dev Team | Fase 2 |
| Testes Automatizados | 2h | QA Team | Fase 3 |
| Validação | 2h | Dev Team | Fase 4 |
| **Total** | **10h** | | |

## Detalhamento dos Testes

### Testes Unitários Críticos

#### Serviços Core
```bash
# Testes de serviços essenciais
uv run pytest tests/test_async_embedding_service.py -v
uv run pytest tests/test_async_llm_provider_service.py -v
uv run pytest tests/test_crawl_orchestration_isolated.py -v
uv run pytest tests/test_rag_simple.py -v
uv run pytest tests/test_business_logic.py -v
```

#### Testes de API
```bash
# Testes de endpoints críticos
uv run pytest tests/test_api_essentials.py -v
uv run pytest tests/test_settings_api.py -v
uv run pytest tests/test_service_integration.py -v
```

#### Testes MCP
```bash
# Testes do servidor MCP
uv run pytest tests/mcp_server/ -v
```

### Testes de Integração Específicos

#### 1. Teste de Conectividade de Serviços
```bash
#!/bin/bash
# scripts/test-service-connectivity.sh

echo "Testando conectividade entre serviços..."

# Aguardar inicialização
sleep 30

# Testar Server
curl -f http://localhost:8181/health || exit 1
echo "✅ Server health check passou"

# Testar MCP
curl -f http://localhost:8051/health || exit 1
echo "✅ MCP health check passou"

# Testar Agents
curl -f http://localhost:8052/health || exit 1
echo "✅ Agents health check passou"

# Testar comunicação MCP -> Server
curl -X POST http://localhost:8051/test-server-connection || exit 1
echo "✅ MCP -> Server comunicação OK"

# Testar comunicação Agents -> MCP
curl -X POST http://localhost:8052/test-mcp-connection || exit 1
echo "✅ Agents -> MCP comunicação OK"

echo "🎉 Todos os testes de conectividade passaram!"
```

#### 2. Teste de Funcionalidades End-to-End
```bash
#!/bin/bash
# scripts/test-e2e-functionality.sh

echo "Executando testes end-to-end..."

# Teste 1: Criação de projeto
PROJECT_ID=$(curl -X POST http://localhost:8181/api/projects \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Project","description":"Migration test"}' \
  | jq -r '.id')

echo "✅ Projeto criado: $PROJECT_ID"

# Teste 2: Upload de documento
curl -X POST http://localhost:8181/api/projects/$PROJECT_ID/documents \
  -F "file=@test-document.pdf" \
  -F "title=Test Document" || exit 1

echo "✅ Documento enviado"

# Teste 3: Crawling de URL
curl -X POST http://localhost:8181/api/projects/$PROJECT_ID/crawl \
  -H "Content-Type: application/json" \
  -d '{"url":"https://example.com","title":"Test Crawl"}' || exit 1

echo "✅ Crawling executado"

# Teste 4: Chat com agente
curl -X POST http://localhost:8052/chat \
  -H "Content-Type: application/json" \
  -d '{"agent_type":"rag","prompt":"Summarize the documents","context":{"project_id":"'$PROJECT_ID'"}}' || exit 1

echo "✅ Chat com agente funcionando"

echo "🎉 Todos os testes E2E passaram!"
```

### Testes de Performance

#### Benchmark de Inicialização
```bash
#!/bin/bash
# scripts/benchmark-startup.sh

echo "Medindo tempo de inicialização..."

# Parar containers
docker-compose down

# Medir tempo de build
echo "Medindo tempo de build..."
time docker-compose build --no-cache

# Medir tempo de inicialização
echo "Medindo tempo de inicialização..."
start_time=$(date +%s)
docker-compose up -d

# Aguardar todos os health checks
while ! curl -f http://localhost:8181/health >/dev/null 2>&1; do
  sleep 1
done

while ! curl -f http://localhost:8051/health >/dev/null 2>&1; do
  sleep 1
done

while ! curl -f http://localhost:8052/health >/dev/null 2>&1; do
  sleep 1
done

end_time=$(date +%s)
startup_time=$((end_time - start_time))

echo "⏱️ Tempo de inicialização: ${startup_time}s"

# Salvar métrica para comparação
echo "startup_time_python312: ${startup_time}s" >> metrics.log
```

#### Teste de Uso de Memória
```bash
#!/bin/bash
# scripts/monitor-memory.sh

echo "Monitorando uso de memória..."

# Coletar métricas por 5 minutos
for i in {1..30}; do
  echo "=== Medição $i/30 ===" >> memory_usage.log
  docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}\t{{.MemPerc}}" >> memory_usage.log
  sleep 10
done

echo "📊 Métricas de memória salvas em memory_usage.log"
```

## Validação de Dependências Específicas

### Playwright (Crawl4AI)
```bash
# Teste específico do Playwright após migração
docker exec archon-server python -c "
import playwright
from playwright.async_api import async_playwright
print('✅ Playwright importado com sucesso')
"

# Verificar browsers instalados
docker exec archon-server playwright install --dry-run
```

### PydanticAI
```bash
# Teste específico do PydanticAI
docker exec archon-agents python -c "
from pydantic_ai import Agent
print('✅ PydanticAI importado com sucesso')
"
```

### Supabase
```bash
# Teste de conectividade Supabase
docker exec archon-server python -c "
from supabase import create_client
import os
url = os.getenv('SUPABASE_URL', 'https://test.supabase.co')
key = os.getenv('SUPABASE_SERVICE_KEY', 'test-key')
client = create_client(url, key)
print('✅ Cliente Supabase criado com sucesso')
"
```

## Monitoramento Pós-Migração

### Métricas a Acompanhar (48h)

#### Performance
- **Tempo de resposta das APIs** (baseline vs pós-migração)
- **Uso de CPU** por container
- **Uso de memória** por container
- **Tempo de inicialização** dos serviços

#### Funcionalidade
- **Taxa de sucesso** das operações de crawling
- **Taxa de sucesso** dos uploads de documentos
- **Latência** das respostas dos agentes
- **Estabilidade** das conexões WebSocket

#### Logs a Monitorar
```bash
# Monitorar logs em tempo real
docker-compose logs -f archon-server | grep -E "(ERROR|CRITICAL|Exception)"
docker-compose logs -f archon-mcp | grep -E "(ERROR|CRITICAL|Exception)"
docker-compose logs -f archon-agents | grep -E "(ERROR|CRITICAL|Exception)"
```

### Dashboard de Monitoramento
```bash
#!/bin/bash
# scripts/monitoring-dashboard.sh

echo "=== Dashboard de Monitoramento Python 3.12 ==="
echo ""

# Status dos containers
echo "📦 Status dos Containers:"
docker-compose ps

echo ""

# Health checks
echo "🏥 Health Checks:"
curl -s http://localhost:8181/health | jq '.status' | xargs echo "Server:"
curl -s http://localhost:8051/health | jq '.status' | xargs echo "MCP:"
curl -s http://localhost:8052/health | jq '.status' | xargs echo "Agents:"

echo ""

# Uso de recursos
echo "💾 Uso de Recursos:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

echo ""

# Logs recentes de erro
echo "🚨 Erros Recentes (últimos 10 minutos):"
docker-compose logs --since=10m | grep -E "(ERROR|CRITICAL|Exception)" | tail -5
```

## Checklist Final de Validação

### ✅ Pré-Migração
- [ ] Backup completo realizado
- [ ] Equipe notificada sobre janela de manutenção
- [ ] Ambiente de teste validado
- [ ] Plano de rollback testado

### ✅ Durante a Migração
- [ ] Dockerfiles atualizados para Python 3.12
- [ ] Variáveis de ambiente ajustadas
- [ ] Build dos containers sem erros
- [ ] Testes unitários passando 100%
- [ ] Testes de integração passando 100%

### ✅ Pós-Migração
- [ ] Todos os serviços inicializando corretamente
- [ ] Health checks passando
- [ ] Funcionalidades E2E validadas
- [ ] Performance dentro dos critérios aceitáveis
- [ ] Logs sem erros críticos
- [ ] Monitoramento ativo por 48h

### ✅ Documentação
- [ ] README.md atualizado com nova versão Python
- [ ] CHANGELOG.md documentando a migração
- [ ] Documentação de desenvolvimento atualizada
- [ ] Guias de troubleshooting atualizados

## Próximos Passos

1. **Aprovação do plano** pela equipe técnica
2. **Agendamento da migração** em janela de manutenção
3. **Execução das fases** conforme cronograma
4. **Monitoramento pós-migração** por 48h
5. **Documentação de lições aprendidas**

---

**Nota**: Este plano foi elaborado com base na análise detalhada do código atual. A migração é considerada de **baixo risco** devido ao alinhamento já existente das configurações com Python 3.12. O foco principal está na atualização dos containers Docker e validação completa da funcionalidade.

