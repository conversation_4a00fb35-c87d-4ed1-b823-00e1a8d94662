# SPEC PRP: Python 3.12 Migration

## Transformation Overview

**Objective**: Upgrade Archon Turbo from Python 3.11 to Python 3.12 across all Docker containers while maintaining full functionality and leveraging performance improvements.

## State Documentation

### Current State

```yaml
current_state:
  files:
    - python/Dockerfile.server (Python 3.11)
    - python/Dockerfile.agents (Python 3.11) 
    - python/Dockerfile.mcp (Python 3.11)
  
  behavior: |
    - All Docker containers running Python 3.11
    - pyproject.toml already configured for Python >=3.12
    - Development tools (ruff, mypy) configured for Python 3.12
    - CI/CD pipeline using Python 3.12
    - PYTHONPATH configured for Python 3.11 in Dockerfile.server
  
  issues:
    - Version mismatch between containers (3.11) and project config (3.12)
    - Potential performance improvements not being utilized
    - Inconsistent Python versions across deployment and development
```

### Desired State

```yaml
desired_state:
  files:
    - python/Dockerfile.server (Python 3.12)
    - python/Dockerfile.agents (Python 3.12)
    - python/Dockerfile.mcp (Python 3.12)
  
  behavior: |
    - All Docker containers running Python 3.12
    - Consistent Python version across all environments
    - Utilizing Python 3.12 performance improvements
    - All dependencies compatible and functional
  
  benefits:
    - 10-15% performance improvement (PEP 659 specializing adaptive interpreter)
    - Better error messages with fine-grained error locations
    - Improved type system with TypeVar defaults
    - Per-interpreter GIL preparation for future parallelism
    - Consistent environment reducing deployment issues
```

## Hierarchical Objectives

### Level 1: Strategic Goal
- **Objective**: Complete Python 3.12 migration with zero downtime
- **Success Metric**: All services running Python 3.12 with passing health checks

### Level 2: System Components
1. **Docker Infrastructure**
   - Update all Dockerfiles to Python 3.12
   - Ensure build process succeeds
   
2. **Application Services**
   - Validate FastAPI server functionality
   - Verify MCP server operations
   - Confirm AI agents service stability

3. **Testing & Validation**
   - Execute comprehensive test suite
   - Perform end-to-end functionality tests
   - Benchmark performance metrics

### Level 3: Detailed Tasks

## Task Specifications

### Phase 1: Preparation

#### Task 1.1: Create Migration Branch
```yaml
create_migration_branch:
  action: CREATE
  type: git_branch
  commands:
    - git checkout -b migration/python-3.12
    - git push -u origin migration/python-3.12
  validation:
    - command: git branch --show-current
    - expect: migration/python-3.12
```

#### Task 1.2: Backup Current State
```yaml
backup_lock_files:
  action: COPY
  files:
    - source: python/uv.lock
    - destination: python/uv.lock.backup
  validation:
    - command: test -f python/uv.lock.backup
    - expect: file exists
```

### Phase 2: Docker Updates

#### Task 2.1: Update Dockerfile.server
```yaml
update_dockerfile_server:
  action: MODIFY
  file: python/Dockerfile.server
  changes: |
    Line 4: FROM python:3.11 AS builder → FROM python:3.12 AS builder
    Line 18: FROM python:3.11-slim → FROM python:3.12-slim
    Line 61: PYTHONPATH="/app:/root/.local/lib/python3.11/site-packages:$PYTHONPATH" 
           → PYTHONPATH="/app:/root/.local/lib/python3.12/site-packages:$PYTHONPATH"
  validation:
    - command: grep "FROM python:3.12" python/Dockerfile.server | wc -l
    - expect: "2"
    - command: grep "python3.12" python/Dockerfile.server
    - expect: site-packages path contains python3.12
```

#### Task 2.2: Update Dockerfile.agents
```yaml
update_dockerfile_agents:
  action: MODIFY
  file: python/Dockerfile.agents
  changes: |
    Line 2: FROM python:3.11-slim → FROM python:3.12-slim
  validation:
    - command: grep "FROM python:3.12-slim" python/Dockerfile.agents
    - expect: match found
```

#### Task 2.3: Update Dockerfile.mcp
```yaml
update_dockerfile_mcp:
  action: MODIFY
  file: python/Dockerfile.mcp
  changes: |
    Line 2: FROM python:3.11-slim → FROM python:3.12-slim
  validation:
    - command: grep "FROM python:3.12-slim" python/Dockerfile.mcp
    - expect: match found
```

### Phase 3: Build & Initial Testing

#### Task 3.1: Build Docker Images
```yaml
build_docker_images:
  action: BUILD
  type: docker
  commands:
    - docker-compose build --no-cache
  validation:
    - command: docker images | grep archon | grep latest
    - expect: all three images present (server, agents, mcp)
```

#### Task 3.2: Run Unit Tests
```yaml
run_unit_tests:
  action: TEST
  directory: python
  commands:
    - cd python
    - uv run pytest tests/test_api_essentials.py -v
    - uv run pytest tests/test_business_logic.py -v
    - uv run pytest tests/test_rag_simple.py -v
  validation:
    - command: echo $?
    - expect: 0 (all tests pass)
```

### Phase 4: Integration Testing

#### Task 4.1: Start Services
```yaml
start_services:
  action: DEPLOY
  commands:
    - docker-compose up -d
  validation:
    - command: docker-compose ps | grep Up | wc -l
    - expect: 3 or more services running
```

#### Task 4.2: Health Check Verification
```yaml
verify_health_checks:
  action: TEST
  type: health_check
  commands:
    - curl -f http://localhost:8181/health
    - curl -f http://localhost:8051/health
    - curl -f http://localhost:8052/health
  validation:
    - command: echo $?
    - expect: 0 for all health checks
```

#### Task 4.3: Playwright Browser Verification
```yaml
verify_playwright:
  action: TEST
  type: dependency_check
  commands: |
    docker exec archon-server python -c "
    import playwright
    from playwright.async_api import async_playwright
    print('Playwright functional')
    "
  validation:
    - expect: "Playwright functional"
```

### Phase 5: End-to-End Testing

#### Task 5.1: Test Crawling Functionality
```yaml
test_crawling:
  action: TEST
  type: e2e
  commands: |
    curl -X POST http://localhost:8181/api/knowledge/crawl \
      -H "Content-Type: application/json" \
      -d '{"url":"https://example.com","max_depth":1}'
  validation:
    - expect: HTTP 200 or 201 response
    - expect: task_id in response
```

#### Task 5.2: Test Document Upload
```yaml
test_document_upload:
  action: TEST
  type: e2e
  commands: |
    echo "# Test Document" > test.md
    curl -X POST http://localhost:8181/api/knowledge/upload \
      -F "file=@test.md" \
      -F "title=Test Migration Doc"
  validation:
    - expect: HTTP 200 response
    - expect: document_id in response
```

#### Task 5.3: Test MCP Tool Execution
```yaml
test_mcp_tools:
  action: TEST
  type: mcp
  commands: |
    curl -X GET http://localhost:8051/tools
  validation:
    - expect: list of available tools
    - expect: archon:perform_rag_query present
```

### Phase 6: Performance Validation

#### Task 6.1: Benchmark Startup Time
```yaml
benchmark_startup:
  action: MEASURE
  type: performance
  commands: |
    docker-compose down
    START=$(date +%s)
    docker-compose up -d
    while ! curl -f http://localhost:8181/health >/dev/null 2>&1; do sleep 1; done
    END=$(date +%s)
    echo "Startup time: $((END-START)) seconds"
  validation:
    - expect: startup_time <= baseline * 1.1 (within 10% of original)
```

#### Task 6.2: Memory Usage Check
```yaml
check_memory_usage:
  action: MEASURE
  type: resource
  commands: |
    docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}"
  validation:
    - expect: memory_usage <= baseline * 1.05 (within 5% of original)
```

### Phase 7: Documentation & Cleanup

#### Task 7.1: Update Documentation
```yaml
update_documentation:
  action: MODIFY
  files:
    - CLAUDE.md
    - README.md
  changes: |
    - Update Python version references from 3.11 to 3.12
    - Add migration completion note
    - Update development setup instructions
  validation:
    - command: grep "Python 3.12" README.md
    - expect: version reference found
```

#### Task 7.2: Commit Changes
```yaml
commit_migration:
  action: CREATE
  type: git_commit
  commands:
    - git add python/Dockerfile.*
    - git add README.md CLAUDE.md
    - git commit -m "feat: upgrade to Python 3.12

      - Update all Docker containers from Python 3.11 to 3.12
      - Align container versions with pyproject.toml configuration
      - Update PYTHONPATH for Python 3.12 site-packages
      - Leverage Python 3.12 performance improvements
      
      All tests passing, health checks green."
  validation:
    - command: git log --oneline -1
    - expect: commit message contains "Python 3.12"
```

## Risk Mitigation

### Identified Risks

1. **Playwright Browser Compatibility**
   - Risk: Browsers may need reinstallation
   - Mitigation: Explicit browser install in Dockerfile
   - Rollback: Keep backup of working image

2. **Dependency Version Conflicts**
   - Risk: Unexpected incompatibilities
   - Mitigation: Comprehensive testing before deployment
   - Rollback: Restore from backup branch

3. **Performance Regression**
   - Risk: Unexpected performance issues
   - Mitigation: Benchmark before and after
   - Rollback: Revert to Python 3.11 images

## Rollback Strategy

```yaml
rollback_procedure:
  triggers:
    - Test suite failure > 5%
    - Health checks failing after 5 minutes
    - Performance degradation > 10%
    - Critical service errors
  
  steps:
    1. Stop services: docker-compose down
    2. Checkout previous version: git checkout HEAD~1 -- python/Dockerfile.*
    3. Rebuild: docker-compose build --no-cache
    4. Restart: docker-compose up -d
    5. Verify: curl health endpoints
    6. Restore lock backup: cp python/uv.lock.backup python/uv.lock
```

## Success Criteria

### Mandatory Requirements
- ✅ All Docker containers running Python 3.12
- ✅ All health checks passing
- ✅ 100% test suite success
- ✅ Core functionality operational:
  - Web crawling
  - Document upload/processing
  - MCP tools execution
  - AI agent interactions

### Performance Targets
- 📊 Startup time: ≤ baseline + 10%
- 📊 Memory usage: ≤ baseline + 5%
- 📊 API response time: ≤ baseline + 5%
- 📊 Test execution time: ≤ baseline

### Quality Gates
- 🔍 Zero critical errors in logs
- 🔍 Ruff linting: zero errors
- 🔍 MyPy type checking: zero errors
- 🔍 Docker image sizes: ≤ baseline + 10%

## Implementation Timeline

| Phase | Duration | Dependencies |
|-------|----------|--------------|
| Preparation | 30 min | None |
| Docker Updates | 30 min | Phase 1 |
| Build & Unit Tests | 1 hour | Phase 2 |
| Integration Testing | 1.5 hours | Phase 3 |
| E2E Testing | 1 hour | Phase 4 |
| Performance Validation | 1 hour | Phase 5 |
| Documentation & Cleanup | 30 min | Phase 6 |
| **Total** | **5.5 hours** | |

## Monitoring Post-Migration

### 48-Hour Observation Period

```yaml
monitoring_checklist:
  performance:
    - API response times (p50, p95, p99)
    - Container restart frequency
    - Memory consumption trends
    - CPU utilization patterns
  
  functionality:
    - Crawling success rate
    - Document processing throughput
    - WebSocket connection stability
    - MCP tool execution reliability
  
  errors:
    - Exception frequency by service
    - Failed request patterns
    - Timeout occurrences
    - Database connection issues
```

## Notes

- This migration is classified as **LOW RISK** due to existing alignment of development tools with Python 3.12
- The primary changes are limited to Docker base images and one PYTHONPATH update
- All dependencies have been verified as Python 3.12 compatible
- The staged approach allows for validation at each step with clear rollback points