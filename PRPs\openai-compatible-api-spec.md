# SPEC PRP: OpenAI-Compatible API Transformation

## Executive Summary

Transform Archon V2 from multi-provider LLM support to a unified OpenAI-compatible API architecture, enabling compatibility with any OpenAI API-compliant provider while simplifying codebase and configuration.

## 1. Current State Assessment

### Architecture
```yaml
current_state:
  files:
    - python/src/server/services/credential_service.py
    - python/src/server/services/llm_provider_service.py
    - python/src/server/services/internal_api.py
    - python/src/agents/document_agent.py
    - python/src/agents/rag_agent.py
    - archon-ui-main/src/components/settings/RAGSettings.tsx
    - archon-ui-main/src/components/onboarding/ProviderStep.tsx
    - archon-ui-main/src/services/credentialsService.ts
  
  behavior:
    - Provider-specific implementations for OpenAI, Google Gemini, Ollama
    - Complex provider selection logic with conditional branches
    - Single configuration for both chat and embedding models
    - Provider-dependent API key management
  
  issues:
    - Maintenance burden from provider-specific code
    - Limited flexibility for new OpenAI-compatible providers
    - Coupling between chat and embedding configurations
    - Complex conditional logic throughout codebase
    - Google Gemini requires non-standard integration
```

## 2. Desired State Definition

### Target Architecture
```yaml
desired_state:
  files:
    - python/migrations/openai_unified_migration.sql [CREATE]
    - python/src/server/services/credential_service.py [MODIFY]
    - python/src/server/services/llm_provider_service.py [MODIFY]
    - python/src/server/services/internal_api.py [MODIFY]
    - python/src/agents/document_agent.py [MODIFY]
    - python/src/agents/rag_agent.py [MODIFY]
    - archon-ui-main/src/components/settings/RAGSettings.tsx [MODIFY]
    - archon-ui-main/src/components/onboarding/ProviderStep.tsx [MODIFY]
    - archon-ui-main/src/services/credentialsService.ts [MODIFY]
    - python/tests/test_openai_compatible_providers.py [CREATE]
  
  behavior:
    - Single OpenAI client implementation
    - Independent chat and embedding configurations
    - Universal compatibility with OpenAI API standard
    - Endpoint validation and caching
    - Clean separation of concerns
  
  benefits:
    - Support for any OpenAI-compatible provider
    - Reduced code complexity by 60%
    - Independent model configuration flexibility
    - Improved maintainability
    - Better error handling and validation
```

## 3. Hierarchical Objectives

### High-Level Objective
**Transform Archon to use unified OpenAI-compatible API architecture**

### Mid-Level Objectives

#### A. Database Schema Evolution
- Create migration script for new configuration structure
- Preserve existing data during migration
- Implement atomic transition

#### B. Backend Service Refactoring
- Simplify credential management
- Unify LLM client creation
- Add endpoint validation

#### C. Frontend Configuration Update
- Redesign settings interface
- Update onboarding flow
- Implement validation UI

#### D. Testing & Validation
- Comprehensive provider testing
- Migration validation
- Integration testing

## 4. Detailed Task Specification

### Phase 1: Database Migration

#### Task 1.1: Create Migration Script
```yaml
task_name: create_openai_migration_script
action: CREATE
file: python/migrations/openai_unified_migration.sql
changes: |
  - CREATE migration script with transaction support
  - ADD new configuration columns:
    - CHAT_MODEL, CHAT_API_KEY, CHAT_BASE_URL
    - EMBEDDING_MODEL, EMBEDDING_API_KEY, EMBEDDING_BASE_URL
  - MIGRATE existing data intelligently based on provider
  - DELETE old provider-specific columns
  - VALIDATE migration success
validation:
  - command: "psql -d archon -f python/migrations/openai_unified_migration.sql"
  - expect: "Migration successful"
```

### Phase 2: Backend Refactoring

#### Task 2.1: Refactor Credential Service
```yaml
task_name: refactor_credential_service
action: MODIFY
file: python/src/server/services/credential_service.py
changes: |
  - DELETE methods: get_active_provider(), _get_provider_api_key(), _get_provider_base_url()
  - ADD method: get_chat_config() -> dict[str, Any]
  - ADD method: get_embedding_config() -> dict[str, Any]  
  - ADD method: validate_openai_endpoint(base_url: str, api_key: str) -> tuple[bool, str]
  - ADD method: invalidate_cache() -> None
  - MODIFY cache structure for new configuration
validation:
  - command: "uv run pytest tests/test_credential_service.py -v"
  - expect: "all tests pass"
```

#### Task 2.2: Simplify LLM Provider Service
```yaml
task_name: simplify_llm_provider_service
action: MODIFY
file: python/src/server/services/llm_provider_service.py
changes: |
  - DELETE all Google Gemini specific logic
  - DELETE all Ollama specific logic
  - DELETE provider selection conditionals
  - MODIFY get_llm_client() to use OpenAI client only
  - ADD cache management with TTL
  - ADD use_embedding_config parameter handling
validation:
  - command: "uv run pytest tests/test_llm_provider_service.py -v"
  - expect: "all tests pass"
```

#### Task 2.3: Update Internal API
```yaml
task_name: update_internal_api
action: MODIFY
file: python/src/server/services/internal_api.py
changes: |
  - MODIFY credential retrieval for new structure
  - UPDATE response format for chat/embedding configs
  - REMOVE provider-specific logic
validation:
  - command: "uv run pytest tests/test_internal_api.py -v"
  - expect: "all tests pass"
```

#### Task 2.4: Update Document Agent
```yaml
task_name: update_document_agent
action: MODIFY
file: python/src/agents/document_agent.py
changes: |
  - MODIFY to use get_embedding_config()
  - UPDATE client initialization
  - REMOVE provider-specific handling
validation:
  - command: "uv run pytest tests/test_document_agent.py -v"
  - expect: "all tests pass"
```

#### Task 2.5: Update RAG Agent
```yaml
task_name: update_rag_agent
action: MODIFY
file: python/src/agents/rag_agent.py
changes: |
  - MODIFY to use get_chat_config()
  - UPDATE client initialization
  - REMOVE provider conditionals
validation:
  - command: "uv run pytest tests/test_rag_agent.py -v"
  - expect: "all tests pass"
```

### Phase 3: Frontend Updates

#### Task 3.1: Redesign RAG Settings
```yaml
task_name: redesign_rag_settings
action: MODIFY
file: archon-ui-main/src/components/settings/RAGSettings.tsx
changes: |
  - CREATE two independent configuration sections
  - ADD Chat Model Configuration panel
  - ADD Embedding Model Configuration panel
  - REMOVE provider dropdown
  - ADD endpoint validation UI
  - ADD test connection functionality
validation:
  - command: "npm run test RAGSettings.test.tsx"
  - expect: "all tests pass"
```

#### Task 3.2: Simplify Provider Step
```yaml
task_name: simplify_provider_step
action: MODIFY
file: archon-ui-main/src/components/onboarding/ProviderStep.tsx
changes: |
  - REMOVE provider selection logic
  - ADD OpenAI-compatible configuration form
  - ADD base URL input with validation
  - ADD quick-setup templates for popular providers
  - UPDATE help text with examples
validation:
  - command: "npm run test ProviderStep.test.tsx"
  - expect: "all tests pass"
```

#### Task 3.3: Update Credentials Service
```yaml
task_name: update_credentials_service_ts
action: MODIFY
file: archon-ui-main/src/services/credentialsService.ts
changes: |
  - ADD getChatConfig() method
  - ADD getEmbeddingConfig() method
  - ADD validateEndpoint() method
  - UPDATE TypeScript interfaces
  - REMOVE provider-specific methods
validation:
  - command: "npm run typecheck"
  - expect: "no type errors"
```

### Phase 4: Testing & Validation

#### Task 4.1: Create Provider Compatibility Tests
```yaml
task_name: create_compatibility_tests
action: CREATE
file: python/tests/test_openai_compatible_providers.py
changes: |
  - ADD test_provider_compatibility() with parameterized providers
  - ADD test_endpoint_validation() for validation logic
  - ADD test_cache_invalidation() for cache behavior
  - ADD test_migration_scenarios() for data migration
  - ADD integration tests for major providers
validation:
  - command: "uv run pytest tests/test_openai_compatible_providers.py -v"
  - expect: "all tests pass"
```

#### Task 4.2: Update Existing Tests
```yaml
task_name: update_existing_tests
action: MODIFY
file: python/tests/test_async_llm_provider_service.py
changes: |
  - REMOVE Google Gemini test cases
  - REMOVE Ollama-specific tests
  - UPDATE mocks for new structure
  - ADD OpenAI-compatible provider tests
validation:
  - command: "uv run pytest tests/ -v"
  - expect: "all tests pass"
```

### Phase 5: Cleanup & Documentation

#### Task 5.1: Remove Legacy Code
```yaml
task_name: remove_legacy_code
action: DELETE
changes: |
  - DELETE all Google Gemini references
  - DELETE all Ollama-specific code
  - DELETE provider mapping logic
  - DELETE unused imports
  - DELETE deprecated configuration keys
validation:
  - command: "uv run ruff check"
  - expect: "no linting errors"
```

#### Task 5.2: Update Documentation
```yaml
task_name: update_documentation
action: MODIFY
file: README.md
changes: |
  - UPDATE configuration section
  - ADD migration guide
  - ADD provider compatibility list
  - ADD troubleshooting section
  - UPDATE environment variable examples
validation:
  - command: "grep -c 'OpenAI-compatible' README.md"
  - expect: "at least 3 occurrences"
```

## 5. Implementation Strategy

### Dependency Order
1. Database Migration (Task 1.1) - Foundation
2. Backend Services (Tasks 2.1-2.5) - Core logic
3. Frontend Updates (Tasks 3.1-3.3) - User interface
4. Testing (Tasks 4.1-4.2) - Validation
5. Cleanup (Tasks 5.1-5.2) - Finalization

### Critical Path
```
Migration Script → Credential Service → LLM Provider Service → Agents → Frontend → Tests
```

## 6. Risk Assessment & Mitigation

### Risk 1: Data Loss During Migration
- **Severity**: High
- **Probability**: Low
- **Mitigation**: 
  - Use database transactions
  - Create backup before migration
  - Test migration on copy first
  - Implement validation checks

### Risk 2: Breaking Existing Configurations
- **Severity**: Medium
- **Probability**: Medium
- **Mitigation**:
  - Intelligent migration based on provider
  - Clear user communication
  - Migration guide documentation
  - Support for manual recovery

### Risk 3: Provider Incompatibility
- **Severity**: Low
- **Probability**: Low
- **Mitigation**:
  - Endpoint validation before save
  - Test connection functionality
  - Comprehensive error messages
  - Provider-specific documentation

## 7. Rollback Strategy

### Database Rollback
```sql
-- Restore from backup
RESTORE DATABASE archon FROM '/backup/archon_pre_migration.sql';
```

### Code Rollback
```bash
# Revert to previous commit
git revert HEAD~1
docker-compose down
docker-compose up --build
```

## 8. Success Criteria

### Functional Criteria
- [ ] All OpenAI-compatible providers work
- [ ] Independent chat/embedding configuration
- [ ] Existing data successfully migrated
- [ ] No provider-specific code remains
- [ ] All tests pass

### Performance Criteria
- [ ] API response time < 200ms
- [ ] Cache hit rate > 80%
- [ ] Zero data loss during migration
- [ ] Endpoint validation < 5 seconds

### Quality Criteria
- [ ] Code coverage > 80%
- [ ] No linting errors
- [ ] Type checking passes
- [ ] Documentation complete

## 9. Testing Matrix

| Provider | Base URL | Expected Result |
|----------|----------|-----------------|
| OpenAI | https://api.openai.com/v1 | ✅ Full compatibility |
| Azure OpenAI | https://*.openai.azure.com | ✅ Full compatibility |
| Ollama | http://localhost:11434/v1 | ✅ Full compatibility |
| LM Studio | http://localhost:1234/v1 | ✅ Full compatibility |
| Together AI | https://api.together.xyz/v1 | ✅ Full compatibility |
| Groq | https://api.groq.com/openai/v1 | ✅ Full compatibility |
| Google Gemini | N/A | ❌ Requires proxy adapter |

## 10. User Communication

### Pre-Migration Notice
```
IMPORTANT: System will migrate to OpenAI-compatible API standard.
- OpenAI users: No action required
- Ollama users: Minimal configuration update
- Google Gemini users: Please see migration guide
```

### Post-Migration Confirmation
```
✅ Migration Complete
- Configuration updated successfully
- Please verify your settings in Settings > LLM Configuration
- Test your connection before proceeding
```

## 11. Monitoring & Observability

### Key Metrics
- Endpoint validation success rate
- Cache hit/miss ratio
- API call latency
- Error rate by provider
- Migration completion rate

### Log Points
```python
logger.info(f"OpenAI endpoint configured: {base_url}")
logger.info(f"Validation result: {base_url} - {is_valid}")
logger.info(f"Cache performance: {hits}/{total}")
logger.error(f"Endpoint failure: {base_url} - {error}")
```

## 12. Timeline

| Phase | Duration | Dependencies |
|-------|----------|--------------|
| Phase 1: Database | 1 day | None |
| Phase 2: Backend | 3 days | Phase 1 |
| Phase 3: Frontend | 2 days | Phase 2 |
| Phase 4: Testing | 2 days | Phase 3 |
| Phase 5: Cleanup | 1 day | Phase 4 |
| **Total** | **9 days** | Sequential |

## Approval Checklist

- [ ] Technical approach validated
- [ ] Migration strategy approved
- [ ] Risk mitigations acceptable
- [ ] Timeline realistic
- [ ] Resources available
- [ ] Documentation plan approved
- [ ] Testing strategy comprehensive
- [ ] Rollback plan tested

---

**Ready for Implementation**: This SPEC PRP provides a complete transformation path from multi-provider to unified OpenAI-compatible architecture with clear objectives, detailed tasks, and comprehensive validation.